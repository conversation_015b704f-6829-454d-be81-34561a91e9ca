/**
 * 骨架屏加载组件
 * 提供各种类型的骨架屏加载状态
 */
export class SkeletonLoader {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            count: 6, // 骨架屏数量
            animation: 'pulse', // 动画类型: 'pulse', 'wave', 'none'
            theme: 'light', // 主题: 'light', 'dark'
            ...options
        };
    }

    /**
     * 显示搜索结果骨架屏
     */
    showSearchResultsSkeleton() {
        const skeletons = [];
        
        for (let i = 0; i < this.options.count; i++) {
            skeletons.push(this.createSearchResultSkeleton());
        }
        
        this.render(skeletons);
    }

    /**
     * 创建搜索结果骨架屏
     */
    createSearchResultSkeleton() {
        return `
            <div class="skeleton-item result-skeleton ${this.getAnimationClass()}">
                <div class="skeleton-content">
                    <div class="skeleton-header">
                        <div class="skeleton-title skeleton-line"></div>
                        <div class="skeleton-platform skeleton-badge"></div>
                    </div>
                    <div class="skeleton-meta">
                        <div class="skeleton-size skeleton-text"></div>
                        <div class="skeleton-time skeleton-text"></div>
                    </div>
                    <div class="skeleton-actions">
                        <div class="skeleton-button"></div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 显示列表骨架屏
     */
    showListSkeleton() {
        const skeletons = [];
        
        for (let i = 0; i < this.options.count; i++) {
            skeletons.push(this.createListItemSkeleton());
        }
        
        this.render(skeletons);
    }

    /**
     * 创建列表项骨架屏
     */
    createListItemSkeleton() {
        return `
            <div class="skeleton-item list-skeleton ${this.getAnimationClass()}">
                <div class="skeleton-avatar"></div>
                <div class="skeleton-content">
                    <div class="skeleton-title skeleton-line"></div>
                    <div class="skeleton-subtitle skeleton-line"></div>
                </div>
            </div>
        `;
    }

    /**
     * 显示卡片骨架屏
     */
    showCardSkeleton() {
        const skeletons = [];
        
        for (let i = 0; i < this.options.count; i++) {
            skeletons.push(this.createCardSkeleton());
        }
        
        this.render(skeletons);
    }

    /**
     * 创建卡片骨架屏
     */
    createCardSkeleton() {
        return `
            <div class="skeleton-item card-skeleton ${this.getAnimationClass()}">
                <div class="skeleton-image"></div>
                <div class="skeleton-content">
                    <div class="skeleton-title skeleton-line"></div>
                    <div class="skeleton-text skeleton-line"></div>
                    <div class="skeleton-text skeleton-line short"></div>
                </div>
            </div>
        `;
    }

    /**
     * 显示表格骨架屏
     */
    showTableSkeleton() {
        const rows = [];
        
        for (let i = 0; i < this.options.count; i++) {
            rows.push(this.createTableRowSkeleton());
        }
        
        const tableSkeleton = `
            <div class="skeleton-table ${this.getAnimationClass()}">
                <div class="skeleton-table-header">
                    <div class="skeleton-th skeleton-line"></div>
                    <div class="skeleton-th skeleton-line"></div>
                    <div class="skeleton-th skeleton-line"></div>
                    <div class="skeleton-th skeleton-line"></div>
                </div>
                <div class="skeleton-table-body">
                    ${rows.join('')}
                </div>
            </div>
        `;
        
        this.container.innerHTML = tableSkeleton;
    }

    /**
     * 创建表格行骨架屏
     */
    createTableRowSkeleton() {
        return `
            <div class="skeleton-tr">
                <div class="skeleton-td skeleton-line"></div>
                <div class="skeleton-td skeleton-line"></div>
                <div class="skeleton-td skeleton-line"></div>
                <div class="skeleton-td skeleton-line"></div>
            </div>
        `;
    }

    /**
     * 显示自定义骨架屏
     */
    showCustomSkeleton(template) {
        const skeletons = [];
        
        for (let i = 0; i < this.options.count; i++) {
            skeletons.push(template);
        }
        
        this.render(skeletons);
    }

    /**
     * 渲染骨架屏
     */
    render(skeletons) {
        const skeletonHTML = `
            <div class="skeleton-container ${this.getThemeClass()}">
                ${skeletons.join('')}
            </div>
        `;
        
        this.container.innerHTML = skeletonHTML;
    }

    /**
     * 获取动画类名
     */
    getAnimationClass() {
        return `skeleton-${this.options.animation}`;
    }

    /**
     * 获取主题类名
     */
    getThemeClass() {
        return `skeleton-theme-${this.options.theme}`;
    }

    /**
     * 隐藏骨架屏
     */
    hide() {
        this.container.innerHTML = '';
    }

    /**
     * 设置骨架屏数量
     */
    setCount(count) {
        this.options.count = count;
    }

    /**
     * 设置动画类型
     */
    setAnimation(animation) {
        this.options.animation = animation;
    }

    /**
     * 设置主题
     */
    setTheme(theme) {
        this.options.theme = theme;
    }

    /**
     * 创建内联骨架屏样式
     */
    static createStyles() {
        const styles = `
            <style>
                .skeleton-container {
                    width: 100%;
                }
                
                .skeleton-item {
                    margin-bottom: 16px;
                    padding: 16px;
                    border-radius: 8px;
                    background: #f8f9fa;
                }
                
                .skeleton-line,
                .skeleton-text,
                .skeleton-badge,
                .skeleton-button,
                .skeleton-avatar,
                .skeleton-image {
                    background: linear-gradient(90deg, #e2e8f0 25%, #f1f5f9 50%, #e2e8f0 75%);
                    background-size: 200% 100%;
                    border-radius: 4px;
                }
                
                .skeleton-pulse .skeleton-line,
                .skeleton-pulse .skeleton-text,
                .skeleton-pulse .skeleton-badge,
                .skeleton-pulse .skeleton-button,
                .skeleton-pulse .skeleton-avatar,
                .skeleton-pulse .skeleton-image {
                    animation: skeleton-pulse 1.5s ease-in-out infinite;
                }
                
                .skeleton-wave .skeleton-line,
                .skeleton-wave .skeleton-text,
                .skeleton-wave .skeleton-badge,
                .skeleton-wave .skeleton-button,
                .skeleton-wave .skeleton-avatar,
                .skeleton-wave .skeleton-image {
                    animation: skeleton-wave 1.5s linear infinite;
                }
                
                @keyframes skeleton-pulse {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.5; }
                }
                
                @keyframes skeleton-wave {
                    0% { background-position: -200% 0; }
                    100% { background-position: 200% 0; }
                }
                
                .skeleton-line {
                    height: 16px;
                    margin-bottom: 8px;
                }
                
                .skeleton-line.short {
                    width: 60%;
                }
                
                .skeleton-text {
                    height: 12px;
                    margin-bottom: 6px;
                }
                
                .skeleton-badge {
                    height: 20px;
                    width: 80px;
                    border-radius: 10px;
                }
                
                .skeleton-button {
                    height: 32px;
                    width: 100px;
                    border-radius: 6px;
                }
                
                .skeleton-avatar {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                }
                
                .skeleton-image {
                    width: 100%;
                    height: 120px;
                    border-radius: 6px;
                }
                
                /* 搜索结果骨架屏样式 */
                .result-skeleton {
                    border: 1px solid #e2e8f0;
                }
                
                .result-skeleton .skeleton-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 12px;
                }
                
                .result-skeleton .skeleton-title {
                    flex: 1;
                    margin-right: 12px;
                }
                
                .result-skeleton .skeleton-meta {
                    display: flex;
                    gap: 12px;
                    margin-bottom: 12px;
                }
                
                .result-skeleton .skeleton-meta .skeleton-text {
                    width: 80px;
                }
                
                /* 深色主题 */
                .skeleton-theme-dark .skeleton-item {
                    background: #1a1a1a;
                }
                
                .skeleton-theme-dark .skeleton-line,
                .skeleton-theme-dark .skeleton-text,
                .skeleton-theme-dark .skeleton-badge,
                .skeleton-theme-dark .skeleton-button,
                .skeleton-theme-dark .skeleton-avatar,
                .skeleton-theme-dark .skeleton-image {
                    background: linear-gradient(90deg, #2d3748 25%, #4a5568 50%, #2d3748 75%);
                    background-size: 200% 100%;
                }
            </style>
        `;
        
        // 如果样式还没有添加到页面中，则添加
        if (!document.querySelector('#skeleton-styles')) {
            const styleElement = document.createElement('div');
            styleElement.id = 'skeleton-styles';
            styleElement.innerHTML = styles;
            document.head.appendChild(styleElement);
        }
    }
}

// 自动添加样式
SkeletonLoader.createStyles();
