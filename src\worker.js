import { handleSearchAPI, handleCORSPreflight } from './api/search.js';
import { generateHTML } from './templates/template-generator.js';
import { getCORSHeaders, getSecurityHeaders } from './config/security.js';

/**
 * Cloudflare Workers 网盘搜索应用
 * 模块化重构版本
 */
export default {
    async fetch(request, _env, _ctx) {
        const url = new URL(request.url);

        try {
            // 处理 CORS 预检请求
            if (request.method === 'OPTIONS') {
                return handleCORSPreflight(request);
            }

            // 处理搜索 API 请求
            if (url.pathname === '/api/search') {
                return handleSearchAPI(request);
            }

            // 处理静态资源请求（主页）
            if (url.pathname === '/') {
                return handleHomePage(request);
            }

            // 404 处理
            const origin = request.headers.get('Origin');
            const environment = process.env.NODE_ENV || 'production';

            return new Response('Not Found', {
                status: 404,
                headers: {
                    'Content-Type': 'text/plain',
                    ...getCORSHeaders(origin, environment)
                }
            });
        } catch (error) {
            console.error('Worker error:', error);
            const origin = request.headers.get('Origin');
            const environment = process.env.NODE_ENV || 'production';

            return new Response('Internal Server Error', {
                status: 500,
                headers: {
                    'Content-Type': 'text/plain',
                    ...getCORSHeaders(origin, environment)
                }
            });
        }
    }
};

/**
 * 处理主页请求
 * @param {Request} request - 请求对象
 * @returns {Response} HTML 响应
 */
async function handleHomePage(request) {
    try {
        const html = await generateHTML();
        const origin = request.headers.get('Origin');
        const environment = process.env.NODE_ENV || 'production';

        return new Response(html, {
            headers: {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'public, max-age=3600',
                ...getSecurityHeaders(origin, environment)
            }
        });
    } catch (error) {
        console.error('Homepage generation error:', error);
        const origin = request.headers.get('Origin');
        const environment = process.env.NODE_ENV || 'production';

        return new Response('Error generating page', {
            status: 500,
            headers: {
                'Content-Type': 'text/plain',
                ...getCORSHeaders(origin, environment)
            }
        });
    }
}
