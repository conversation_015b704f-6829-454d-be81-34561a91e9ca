{"name": "pansou-search", "version": "1.0.0", "description": "网盘搜索应用 - 基于 Cloudflare Workers", "type": "module", "main": "worker.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "prepare": "husky", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "pre-commit": "lint-staged", "build": "webpack --config src/build/webpack.config.js --mode production", "build:dev": "webpack --config src/build/webpack.config.js --mode development", "build:watch": "webpack --config src/build/webpack.config.js --mode development --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:performance": "lighthouse-ci autorun", "test:performance:local": "node scripts/performance-test.js", "test:all": "npm run test && npm run test:e2e", "security:audit": "npm audit", "security:audit:fix": "npm audit fix", "security:snyk": "snyk test", "security:snyk:monitor": "snyk monitor", "security:scan": "node scripts/security-scan.js", "security:all": "npm run security:audit && npm run security:snyk"}, "keywords": ["cloudflare-workers", "search", "file-storage", "pan-search"], "author": "Your Name", "license": "MIT", "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@eslint/js": "^9.31.0", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "eslint": "^9.31.0", "html-loader": "^5.1.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "rimraf": "^6.0.1", "style-loader": "^4.0.0", "webpack": "^5.100.2", "webpack-cli": "^6.0.1", "wrangler": "^4.24.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,html,css}": ["prettier --write"]}, "dependencies": {"dompurify": "^3.2.6", "zod": "^4.0.10"}}