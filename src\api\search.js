import { API_CONFIG, ERROR_MESSAGES } from '../config/constants.js';
import { getSecurityHeaders, getCORSHeaders } from '../config/security.js';
import {
    NetworkError,
    TimeoutError
} from '../scripts/utils/error-handler.js';
import {
    EnhancedValidator,
    ValidationError
} from '../scripts/utils/validation.js';

/**
 * 处理搜索 API 请求
 * @param {Request} request - 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleSearchAPI(request) {
    try {
        const url = new URL(request.url);
        const kw = url.searchParams.get('kw');

        // 验证搜索关键词
        try {
            const validatedKeyword = EnhancedValidator.validateSearchKeyword(kw);

            // 验证所有搜索参数
            const searchParams = {
                kw: validatedKeyword,
                refresh: url.searchParams.get('refresh'),
                res: url.searchParams.get('res'),
                src: url.searchParams.get('src'),
                plugins: url.searchParams.get('plugins')
            };

            const validatedParams = EnhancedValidator.validateSearchParams(searchParams);
        } catch (error) {
            const origin = request.headers.get('Origin');
            if (error instanceof ValidationError) {
                return createErrorResponse(error.message, 400, origin);
            }
            throw error;
        }

        // 构建搜索 API URL
        const searchUrl = buildSearchUrl(url);

        // 调用外部搜索 API
        const response = await fetch(searchUrl.toString(), {
            method: 'GET',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            signal: AbortSignal.timeout(API_CONFIG.REQUEST_TIMEOUT)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.text();

        // 获取请求源
        const origin = request.headers.get('Origin');
        const environment = process.env.NODE_ENV || 'production';

        return new Response(data, {
            headers: {
                'Content-Type': 'application/json',
                ...getSecurityHeaders(origin, environment)
            }
        });
    } catch (error) {
        console.error('Search API error:', error);
        const origin = request.headers.get('Origin');

        if (error instanceof TimeoutError || error.name === 'TimeoutError') {
            return createErrorResponse(ERROR_MESSAGES.TIMEOUT_ERROR, 408, origin);
        }

        if (error instanceof NetworkError) {
            return createErrorResponse(ERROR_MESSAGES.NETWORK_ERROR, 503, origin);
        }

        if (error instanceof ValidationError) {
            return createErrorResponse(error.message, 400, origin);
        }

        return createErrorResponse(ERROR_MESSAGES.SEARCH_FAILED, 500, origin);
    }
}

/**
 * 构建搜索 URL
 * @param {URL} requestUrl - 请求 URL
 * @returns {URL} 搜索 API URL
 */
function buildSearchUrl(requestUrl) {
    const searchUrl = new URL(API_CONFIG.SEARCH_BASE_URL);

    // 复制请求参数
    const kw = requestUrl.searchParams.get('kw');
    searchUrl.searchParams.set('kw', kw);

    // 设置默认参数
    const params = {
        refresh: requestUrl.searchParams.get('refresh') || API_CONFIG.DEFAULT_SEARCH_PARAMS.refresh,
        res: requestUrl.searchParams.get('res') || API_CONFIG.DEFAULT_SEARCH_PARAMS.res,
        src: requestUrl.searchParams.get('src') || API_CONFIG.DEFAULT_SEARCH_PARAMS.src,
        plugins: requestUrl.searchParams.get('plugins') || API_CONFIG.DEFAULT_SEARCH_PARAMS.plugins
    };

    Object.entries(params).forEach(([key, value]) => {
        searchUrl.searchParams.set(key, value);
    });

    return searchUrl;
}

/**
 * 创建错误响应
 * @param {string} message - 错误消息
 * @param {number} status - HTTP 状态码
 * @returns {Response} 错误响应
 */
function createErrorResponse(message, status = 500, origin = null) {
    const environment = process.env.NODE_ENV || 'production';

    return new Response(
        JSON.stringify({
            error: message,
            code: status,
            timestamp: new Date().toISOString()
        }),
        {
            status,
            headers: {
                'Content-Type': 'application/json',
                ...getCORSHeaders(origin, environment)
            }
        }
    );
}

/**
 * 处理 CORS 预检请求
 * @param {Request} request - 请求对象
 * @returns {Response} CORS 响应
 */
export function handleCORSPreflight(request) {
    const origin = request.headers.get('Origin');
    const environment = process.env.NODE_ENV || 'production';

    return new Response(null, {
        headers: getCORSHeaders(origin, environment)
    });
}
