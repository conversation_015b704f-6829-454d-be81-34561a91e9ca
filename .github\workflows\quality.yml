name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  # 代码格式和规范检查
  lint-and-format:
    name: Lint and Format Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run ESLint
      run: |
        echo "Running ESLint..."
        npm run lint -- --format=json --output-file=eslint-results.json || true
        npm run lint
        
    - name: Run Prettier check
      run: |
        echo "Checking code formatting..."
        npm run format:check
        
    - name: Upload ESLint results
      uses: actions/upload-artifact@v3
      with:
        name: eslint-results
        path: eslint-results.json
      continue-on-error: true

  # 代码复杂度分析
  complexity-analysis:
    name: Code Complexity Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install complexity analysis tools
      run: npm install -g complexity-report jscpd
      
    - name: Run complexity analysis
      run: |
        echo "Running complexity analysis..."
        complexity-report --format json --output complexity-report.json src/ || true
        
    - name: Run duplicate code detection
      run: |
        echo "Running duplicate code detection..."
        jscpd --format json --output jscpd-report.json src/ || true
        
    - name: Upload complexity results
      uses: actions/upload-artifact@v3
      with:
        name: complexity-analysis
        path: |
          complexity-report.json
          jscpd-report.json
      continue-on-error: true

  # 代码覆盖率分析
  coverage-analysis:
    name: Code Coverage Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests with coverage
      run: |
        echo "Running tests with coverage..."
        npm run test:coverage || true
        
    - name: Generate coverage report
      run: |
        if [ -d "coverage" ]; then
          echo "Coverage report generated"
          ls -la coverage/
        else
          echo "No coverage report found"
        fi
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
      continue-on-error: true
      
    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: coverage/
      continue-on-error: true

  # 文档质量检查
  docs-quality:
    name: Documentation Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Check README
      run: |
        echo "Checking README.md..."
        if [ ! -f "README.md" ]; then
          echo "❌ README.md not found"
          exit 1
        fi
        
        # 检查README长度
        readme_lines=$(wc -l < README.md)
        if [ $readme_lines -lt 10 ]; then
          echo "⚠️ README.md is too short ($readme_lines lines)"
        else
          echo "✅ README.md looks good ($readme_lines lines)"
        fi
        
    - name: Check JSDoc comments
      run: |
        echo "Checking JSDoc comments..."
        # 简单检查是否有JSDoc注释
        jsdoc_count=$(grep -r "\/\*\*" src/ | wc -l)
        echo "Found $jsdoc_count JSDoc comments"
        
        if [ $jsdoc_count -lt 10 ]; then
          echo "⚠️ Consider adding more JSDoc comments"
        else
          echo "✅ Good JSDoc coverage"
        fi
        
    - name: Check for TODO/FIXME comments
      run: |
        echo "Checking for TODO/FIXME comments..."
        todo_count=$(grep -r -i "todo\|fixme" src/ | wc -l)
        echo "Found $todo_count TODO/FIXME comments"
        
        if [ $todo_count -gt 20 ]; then
          echo "⚠️ Too many TODO/FIXME comments ($todo_count)"
        else
          echo "✅ Reasonable number of TODO/FIXME comments"
        fi

  # 依赖分析
  dependency-analysis:
    name: Dependency Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Analyze dependencies
      run: |
        echo "Analyzing dependencies..."
        
        # 检查过时的依赖
        echo "Checking for outdated dependencies..."
        npm outdated --json > outdated-deps.json || true
        
        # 检查未使用的依赖
        echo "Checking for unused dependencies..."
        npx depcheck --json > unused-deps.json || true
        
        # 检查依赖大小
        echo "Analyzing bundle size..."
        npx bundlesize || true
        
    - name: Upload dependency analysis
      uses: actions/upload-artifact@v3
      with:
        name: dependency-analysis
        path: |
          outdated-deps.json
          unused-deps.json
      continue-on-error: true

  # 生成质量报告
  quality-report:
    name: Generate Quality Report
    runs-on: ubuntu-latest
    needs: [lint-and-format, complexity-analysis, coverage-analysis, docs-quality, dependency-analysis]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download all artifacts
      uses: actions/download-artifact@v3
      
    - name: Generate quality report
      run: |
        echo "# Code Quality Report" > quality-report.md
        echo "Generated on: $(date)" >> quality-report.md
        echo "" >> quality-report.md
        
        echo "## Lint Results" >> quality-report.md
        if [ -f "eslint-results/eslint-results.json" ]; then
          echo "ESLint results available" >> quality-report.md
        else
          echo "ESLint results not available" >> quality-report.md
        fi
        
        echo "" >> quality-report.md
        echo "## Complexity Analysis" >> quality-report.md
        if [ -f "complexity-analysis/complexity-report.json" ]; then
          echo "Complexity analysis available" >> quality-report.md
        else
          echo "Complexity analysis not available" >> quality-report.md
        fi
        
        echo "" >> quality-report.md
        echo "## Code Coverage" >> quality-report.md
        if [ -d "coverage-report" ]; then
          echo "Coverage report available" >> quality-report.md
        else
          echo "Coverage report not available" >> quality-report.md
        fi
        
        echo "" >> quality-report.md
        echo "## Dependency Analysis" >> quality-report.md
        if [ -f "dependency-analysis/outdated-deps.json" ]; then
          echo "Dependency analysis available" >> quality-report.md
        else
          echo "Dependency analysis not available" >> quality-report.md
        fi
        
    - name: Upload quality report
      uses: actions/upload-artifact@v3
      with:
        name: quality-report
        path: quality-report.md
        
    - name: Comment PR with quality report
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('quality-report.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 📊 Code Quality Report\n\n${report}`
          });
