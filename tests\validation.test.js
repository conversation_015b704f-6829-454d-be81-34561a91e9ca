import { EnhancedValidator, ValidationError } from '../src/scripts/utils/validation.js';

/**
 * 验证器测试
 */
function runValidationTests() {
    console.log('开始运行验证器测试...');
    
    // 测试搜索关键词验证
    testSearchKeywordValidation();
    
    // 测试页码验证
    testPageValidation();
    
    // 测试每页大小验证
    testPageSizeValidation();
    
    // 测试搜索参数验证
    testSearchParamsValidation();
    
    // 测试文件名验证
    testFilenameValidation();
    
    // 测试URL验证
    testUrlValidation();
    
    // 测试HTML清理
    testHTMLSanitization();
    
    console.log('所有验证器测试完成！');
}

function testSearchKeywordValidation() {
    console.log('测试搜索关键词验证...');
    
    // 有效关键词
    try {
        const result = EnhancedValidator.validateSearchKeyword('测试关键词');
        console.log('✓ 有效关键词验证通过:', result);
    } catch (error) {
        console.error('✗ 有效关键词验证失败:', error.message);
    }
    
    // 空关键词
    try {
        EnhancedValidator.validateSearchKeyword('');
        console.error('✗ 空关键词应该验证失败');
    } catch (error) {
        console.log('✓ 空关键词验证正确失败:', error.message);
    }
    
    // 包含非法字符的关键词
    try {
        EnhancedValidator.validateSearchKeyword('<script>alert("xss")</script>');
        console.error('✗ 包含非法字符的关键词应该验证失败');
    } catch (error) {
        console.log('✓ 非法字符关键词验证正确失败:', error.message);
    }
    
    // 过长关键词
    try {
        const longKeyword = 'a'.repeat(101);
        EnhancedValidator.validateSearchKeyword(longKeyword);
        console.error('✗ 过长关键词应该验证失败');
    } catch (error) {
        console.log('✓ 过长关键词验证正确失败:', error.message);
    }
}

function testPageValidation() {
    console.log('测试页码验证...');
    
    // 有效页码
    try {
        const result = EnhancedValidator.validatePage(1);
        console.log('✓ 有效页码验证通过:', result);
    } catch (error) {
        console.error('✗ 有效页码验证失败:', error.message);
    }
    
    // 无效页码（0）
    try {
        EnhancedValidator.validatePage(0);
        console.error('✗ 页码0应该验证失败');
    } catch (error) {
        console.log('✓ 页码0验证正确失败:', error.message);
    }
    
    // 无效页码（负数）
    try {
        EnhancedValidator.validatePage(-1);
        console.error('✗ 负数页码应该验证失败');
    } catch (error) {
        console.log('✓ 负数页码验证正确失败:', error.message);
    }
}

function testPageSizeValidation() {
    console.log('测试每页大小验证...');
    
    // 有效每页大小
    try {
        const result = EnhancedValidator.validatePageSize(12);
        console.log('✓ 有效每页大小验证通过:', result);
    } catch (error) {
        console.error('✗ 有效每页大小验证失败:', error.message);
    }
    
    // 无效每页大小（不在预定义选项中）
    try {
        EnhancedValidator.validatePageSize(13);
        console.error('✗ 非预定义每页大小应该验证失败');
    } catch (error) {
        console.log('✓ 非预定义每页大小验证正确失败:', error.message);
    }
}

function testSearchParamsValidation() {
    console.log('测试搜索参数验证...');
    
    // 有效搜索参数
    try {
        const params = {
            kw: '测试',
            refresh: 'false',
            res: 'merge',
            src: 'all'
        };
        const result = EnhancedValidator.validateSearchParams(params);
        console.log('✓ 有效搜索参数验证通过:', result);
    } catch (error) {
        console.error('✗ 有效搜索参数验证失败:', error.message);
    }
    
    // 无效搜索参数
    try {
        const params = {
            kw: '',
            refresh: 'invalid',
            res: 'invalid'
        };
        EnhancedValidator.validateSearchParams(params);
        console.error('✗ 无效搜索参数应该验证失败');
    } catch (error) {
        console.log('✓ 无效搜索参数验证正确失败:', error.message);
    }
}

function testFilenameValidation() {
    console.log('测试文件名验证...');
    
    // 有效文件名
    try {
        const result = EnhancedValidator.validateFilename('test.txt');
        console.log('✓ 有效文件名验证通过:', result);
    } catch (error) {
        console.error('✗ 有效文件名验证失败:', error.message);
    }
    
    // 包含非法字符的文件名
    try {
        EnhancedValidator.validateFilename('test<>.txt');
        console.error('✗ 包含非法字符的文件名应该验证失败');
    } catch (error) {
        console.log('✓ 非法字符文件名验证正确失败:', error.message);
    }
}

function testUrlValidation() {
    console.log('测试URL验证...');
    
    // 有效URL
    try {
        const result = EnhancedValidator.validateUrl('https://example.com');
        console.log('✓ 有效URL验证通过:', result);
    } catch (error) {
        console.error('✗ 有效URL验证失败:', error.message);
    }
    
    // 无效URL
    try {
        EnhancedValidator.validateUrl('not-a-url');
        console.error('✗ 无效URL应该验证失败');
    } catch (error) {
        console.log('✓ 无效URL验证正确失败:', error.message);
    }
}

function testHTMLSanitization() {
    console.log('测试HTML清理...');
    
    // 包含脚本的HTML
    const maliciousHTML = '<div>正常内容</div><script>alert("xss")</script>';
    const sanitized = EnhancedValidator.sanitizeHTML(maliciousHTML);
    
    if (sanitized.includes('<script>')) {
        console.error('✗ HTML清理失败，仍包含脚本标签');
    } else {
        console.log('✓ HTML清理成功:', sanitized);
    }
}

// 运行测试
runValidationTests();
