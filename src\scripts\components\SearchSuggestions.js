import { searchHistory } from '../utils/helpers.js';

/**
 * 搜索建议组件
 * 提供搜索建议和历史记录功能
 */
export class SearchSuggestions {
    constructor(searchInput, options = {}) {
        this.searchInput = searchInput;
        this.options = {
            maxSuggestions: 8,
            showHistory: true,
            showPopular: true,
            containerClass: 'search-suggestions',
            itemClass: 'suggestion-item',
            historyClass: 'suggestion-history',
            popularClass: 'suggestion-popular',
            ...options
        };
        
        this.container = null;
        this.isVisible = false;
        this.selectedIndex = -1;
        this.suggestions = [];
        
        this.init();
    }

    init() {
        this.createContainer();
        this.bindEvents();
    }

    createContainer() {
        this.container = document.createElement('div');
        this.container.className = this.options.containerClass;
        this.container.style.display = 'none';
        
        // 插入到搜索框后面
        this.searchInput.parentNode.insertBefore(this.container, this.searchInput.nextSibling);
    }

    bindEvents() {
        // 搜索框输入事件
        this.searchInput.addEventListener('input', (e) => {
            this.handleInput(e.target.value);
        });

        // 搜索框焦点事件
        this.searchInput.addEventListener('focus', () => {
            this.show();
        });

        // 搜索框失焦事件
        this.searchInput.addEventListener('blur', (e) => {
            // 延迟隐藏，以便点击建议项
            setTimeout(() => {
                if (!this.container.contains(document.activeElement)) {
                    this.hide();
                }
            }, 150);
        });

        // 键盘导航
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        // 点击外部隐藏
        document.addEventListener('click', (e) => {
            if (!this.searchInput.contains(e.target) && !this.container.contains(e.target)) {
                this.hide();
            }
        });
    }

    handleInput(value) {
        const trimmedValue = value.trim();
        
        if (trimmedValue.length === 0) {
            this.showDefaultSuggestions();
        } else {
            this.showSearchSuggestions(trimmedValue);
        }
    }

    handleKeydown(e) {
        if (!this.isVisible) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectNext();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.selectPrevious();
                break;
            case 'Enter':
                e.preventDefault();
                this.selectCurrent();
                break;
            case 'Escape':
                this.hide();
                break;
        }
    }

    showDefaultSuggestions() {
        const suggestions = [];
        
        if (this.options.showHistory) {
            const history = searchHistory.get(5);
            suggestions.push(...history.map(item => ({
                text: item.keyword,
                type: 'history',
                count: item.count
            })));
        }
        
        if (this.options.showPopular && suggestions.length < this.options.maxSuggestions) {
            const popular = searchHistory.getPopular(this.options.maxSuggestions - suggestions.length);
            const popularSuggestions = popular
                .filter(item => !suggestions.some(s => s.text === item.keyword))
                .map(item => ({
                    text: item.keyword,
                    type: 'popular',
                    count: item.count
                }));
            suggestions.push(...popularSuggestions);
        }
        
        this.renderSuggestions(suggestions);
    }

    showSearchSuggestions(query) {
        const suggestions = searchHistory.getSuggestions(query, this.options.maxSuggestions);
        const formattedSuggestions = suggestions.map(item => ({
            text: item.keyword,
            type: 'history',
            count: item.count,
            highlight: query
        }));
        
        this.renderSuggestions(formattedSuggestions);
    }

    renderSuggestions(suggestions) {
        this.suggestions = suggestions;
        this.selectedIndex = -1;
        
        if (suggestions.length === 0) {
            this.hide();
            return;
        }
        
        const html = suggestions.map((suggestion, index) => {
            return this.renderSuggestionItem(suggestion, index);
        }).join('');
        
        this.container.innerHTML = html;
        this.bindSuggestionEvents();
        this.show();
    }

    renderSuggestionItem(suggestion, index) {
        const typeClass = suggestion.type === 'history' ? this.options.historyClass : this.options.popularClass;
        const icon = suggestion.type === 'history' ? '🕒' : '🔥';
        const countText = suggestion.count > 1 ? `(${suggestion.count})` : '';
        
        let displayText = suggestion.text;
        if (suggestion.highlight) {
            const regex = new RegExp(`(${suggestion.highlight})`, 'gi');
            displayText = suggestion.text.replace(regex, '<mark>$1</mark>');
        }
        
        return `
            <div class="${this.options.itemClass} ${typeClass}" data-index="${index}" data-text="${suggestion.text}">
                <span class="suggestion-icon">${icon}</span>
                <span class="suggestion-text">${displayText}</span>
                <span class="suggestion-count">${countText}</span>
                <button class="suggestion-remove" data-keyword="${suggestion.text}" title="删除">×</button>
            </div>
        `;
    }

    bindSuggestionEvents() {
        // 点击建议项
        this.container.querySelectorAll(`.${this.options.itemClass}`).forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.classList.contains('suggestion-remove')) {
                    e.stopPropagation();
                    this.removeSuggestion(e.target.dataset.keyword);
                } else {
                    this.selectSuggestion(item.dataset.text);
                }
            });
            
            // 鼠标悬停
            item.addEventListener('mouseenter', () => {
                this.selectedIndex = parseInt(item.dataset.index);
                this.updateSelection();
            });
        });
    }

    selectNext() {
        this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);
        this.updateSelection();
    }

    selectPrevious() {
        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
        this.updateSelection();
    }

    selectCurrent() {
        if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {
            this.selectSuggestion(this.suggestions[this.selectedIndex].text);
        }
    }

    updateSelection() {
        this.container.querySelectorAll(`.${this.options.itemClass}`).forEach((item, index) => {
            if (index === this.selectedIndex) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        });
    }

    selectSuggestion(text) {
        this.searchInput.value = text;
        this.hide();
        
        // 触发搜索事件
        const event = new CustomEvent('suggestionSelected', {
            detail: { keyword: text }
        });
        this.searchInput.dispatchEvent(event);
    }

    removeSuggestion(keyword) {
        searchHistory.remove(keyword);
        this.handleInput(this.searchInput.value);
    }

    show() {
        this.container.style.display = 'block';
        this.isVisible = true;
    }

    hide() {
        this.container.style.display = 'none';
        this.isVisible = false;
        this.selectedIndex = -1;
    }

    destroy() {
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
    }

    static createStyles() {
        const styles = `
            <style id="search-suggestions-styles">
                .search-suggestions {
                    position: absolute;
                    top: 100%;
                    left: 0;
                    right: 0;
                    background: white;
                    border: 1px solid #e2e8f0;
                    border-top: none;
                    border-radius: 0 0 8px 8px;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    z-index: 1000;
                    max-height: 300px;
                    overflow-y: auto;
                }
                
                .suggestion-item {
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    cursor: pointer;
                    border-bottom: 1px solid #f1f5f9;
                    transition: background-color 0.2s;
                }
                
                .suggestion-item:hover,
                .suggestion-item.selected {
                    background-color: #f8fafc;
                }
                
                .suggestion-item:last-child {
                    border-bottom: none;
                }
                
                .suggestion-icon {
                    margin-right: 8px;
                    font-size: 14px;
                }
                
                .suggestion-text {
                    flex: 1;
                    font-size: 14px;
                    color: #374151;
                }
                
                .suggestion-text mark {
                    background-color: #fef3c7;
                    color: #92400e;
                    padding: 0 2px;
                    border-radius: 2px;
                }
                
                .suggestion-count {
                    font-size: 12px;
                    color: #9ca3af;
                    margin-left: 8px;
                }
                
                .suggestion-remove {
                    background: none;
                    border: none;
                    color: #9ca3af;
                    cursor: pointer;
                    font-size: 16px;
                    padding: 0;
                    margin-left: 8px;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    transition: all 0.2s;
                }
                
                .suggestion-remove:hover {
                    background-color: #fee2e2;
                    color: #dc2626;
                }
                
                .suggestion-popular {
                    /* 热门搜索样式 */
                }
                
                .suggestion-history {
                    /* 历史搜索样式 */
                }
            </style>
        `;
        
        if (!document.querySelector('#search-suggestions-styles')) {
            document.head.insertAdjacentHTML('beforeend', styles);
        }
    }
}

// 自动添加样式
SearchSuggestions.createStyles();
