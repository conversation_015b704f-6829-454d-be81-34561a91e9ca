/**
 * 懒加载工具类
 * 提供图片和内容的懒加载功能
 */
export class LazyLoader {
    constructor(options = {}) {
        this.options = {
            rootMargin: '50px', // 提前50px开始加载
            threshold: 0.1,
            loadingClass: 'lazy-loading',
            loadedClass: 'lazy-loaded',
            errorClass: 'lazy-error',
            ...options
        };

        this.observer = null;
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(
                (entries) => this.handleIntersection(entries),
                {
                    rootMargin: this.options.rootMargin,
                    threshold: this.options.threshold
                }
            );
        }
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.loadElement(entry.target);
                this.observer.unobserve(entry.target);
            }
        });
    }

    observe(element) {
        if (this.observer) {
            this.observer.observe(element);
        } else {
            // 降级处理：直接加载
            this.loadElement(element);
        }
    }

    unobserve(element) {
        if (this.observer) {
            this.observer.unobserve(element);
        }
    }

    loadElement(element) {
        const type = element.dataset.lazyType || 'image';
        
        switch (type) {
            case 'image':
                this.loadImage(element);
                break;
            case 'content':
                this.loadContent(element);
                break;
            case 'iframe':
                this.loadIframe(element);
                break;
            default:
                this.loadImage(element);
        }
    }

    loadImage(img) {
        const src = img.dataset.lazySrc;
        if (!src) return;

        img.classList.add(this.options.loadingClass);

        const image = new Image();
        
        image.onload = () => {
            img.src = src;
            img.classList.remove(this.options.loadingClass);
            img.classList.add(this.options.loadedClass);
            
            // 触发自定义事件
            img.dispatchEvent(new CustomEvent('lazyloaded', {
                detail: { src }
            }));
        };

        image.onerror = () => {
            img.classList.remove(this.options.loadingClass);
            img.classList.add(this.options.errorClass);
            
            // 设置错误占位图
            const errorSrc = img.dataset.lazyError || this.getDefaultErrorImage();
            if (errorSrc) {
                img.src = errorSrc;
            }
            
            // 触发错误事件
            img.dispatchEvent(new CustomEvent('lazyerror', {
                detail: { src, error: 'Failed to load image' }
            }));
        };

        image.src = src;
    }

    loadContent(element) {
        const url = element.dataset.lazySrc;
        if (!url) return;

        element.classList.add(this.options.loadingClass);

        fetch(url)
            .then(response => response.text())
            .then(html => {
                element.innerHTML = html;
                element.classList.remove(this.options.loadingClass);
                element.classList.add(this.options.loadedClass);
                
                // 触发自定义事件
                element.dispatchEvent(new CustomEvent('lazyloaded', {
                    detail: { url, content: html }
                }));
            })
            .catch(error => {
                element.classList.remove(this.options.loadingClass);
                element.classList.add(this.options.errorClass);
                element.innerHTML = '<div class="lazy-error-message">加载失败</div>';
                
                // 触发错误事件
                element.dispatchEvent(new CustomEvent('lazyerror', {
                    detail: { url, error: error.message }
                }));
            });
    }

    loadIframe(iframe) {
        const src = iframe.dataset.lazySrc;
        if (!src) return;

        iframe.classList.add(this.options.loadingClass);
        
        iframe.onload = () => {
            iframe.classList.remove(this.options.loadingClass);
            iframe.classList.add(this.options.loadedClass);
            
            // 触发自定义事件
            iframe.dispatchEvent(new CustomEvent('lazyloaded', {
                detail: { src }
            }));
        };

        iframe.onerror = () => {
            iframe.classList.remove(this.options.loadingClass);
            iframe.classList.add(this.options.errorClass);
            
            // 触发错误事件
            iframe.dispatchEvent(new CustomEvent('lazyerror', {
                detail: { src, error: 'Failed to load iframe' }
            }));
        };

        iframe.src = src;
    }

    getDefaultErrorImage() {
        // 返回默认的错误占位图（base64编码的小图片）
        return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBDOTQuNDc3MiA3MCA5MCA3NC40NzcyIDkwIDgwVjEyMEM5MCA5NC40NzcyIDk0LjQ3NzIgOTAgMTAwIDkwSDEwMEMxMDUuNTIzIDkwIDExMCA5NC40NzcyIDExMCAxMjBWODBDMTEwIDc0LjQ3NzIgMTA1LjUyMyA3MCAxMDAgNzBaIiBmaWxsPSIjOUI5QkEwIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEzMCIgcj0iNSIgZmlsbD0iIzlCOUJBMCIvPgo8L3N2Zz4K';
    }

    observeImages(container = document) {
        const images = container.querySelectorAll('img[data-lazy-src]');
        images.forEach(img => this.observe(img));
    }

    observeContent(container = document) {
        const elements = container.querySelectorAll('[data-lazy-type="content"]');
        elements.forEach(element => this.observe(element));
    }

    observeIframes(container = document) {
        const iframes = container.querySelectorAll('iframe[data-lazy-src]');
        iframes.forEach(iframe => this.observe(iframe));
    }

    observeAll(container = document) {
        this.observeImages(container);
        this.observeContent(container);
        this.observeIframes(container);
    }

    disconnect() {
        if (this.observer) {
            this.observer.disconnect();
        }
    }

    static createPlaceholder(width, height, text = '加载中...') {
        return `data:image/svg+xml;base64,${btoa(`
            <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="${width}" height="${height}" fill="#F3F4F6"/>
                <text x="50%" y="50%" text-anchor="middle" dy="0.3em" fill="#9B9BA0" font-family="Arial, sans-serif" font-size="14">${text}</text>
            </svg>
        `)}`;
    }
}

// 创建全局懒加载实例
export const globalLazyLoader = new LazyLoader();

// 便捷函数
export function lazyLoadImages(container = document) {
    globalLazyLoader.observeImages(container);
}

export function lazyLoadContent(container = document) {
    globalLazyLoader.observeContent(container);
}

export function lazyLoadAll(container = document) {
    globalLazyLoader.observeAll(container);
}
