/**
 * 应用配置常量
 * 集中管理所有配置项，避免硬编码
 */

// API 配置
export const API_CONFIG = {
    // 搜索API基础URL
    SEARCH_BASE_URL: 'https://pansou.252035.xyz/api/search',

    // 默认搜索参数
    DEFAULT_SEARCH_PARAMS: {
        refresh: 'false',
        res: 'merge',
        src: 'all',
        plugins: 'pansearch,qupansou,panta,pan666,hunhepan,jikepan'
    },

    // 请求超时时间（毫秒）
    REQUEST_TIMEOUT: 30000,

    // 最大重试次数
    MAX_RETRIES: 3,

    // 重试延迟（毫秒）
    RETRY_DELAY: 1000
};

// 分页配置
export const PAGINATION_CONFIG = {
    // 默认每页显示数量
    DEFAULT_PAGE_SIZE: 12,

    // 每页显示数量选项
    PAGE_SIZE_OPTIONS: [6, 12, 24, 48],

    // 分页按钮显示范围
    PAGE_BUTTON_RANGE: 2
};

// 缓存配置
export const CACHE_CONFIG = {
    // 缓存最大数量
    MAX_CACHE_SIZE: 100,

    // 缓存过期时间（毫秒）
    CACHE_TTL: 5 * 60 * 1000, // 5分钟

    // 是否启用缓存
    ENABLE_CACHE: true
};

// 平台配置
export const PLATFORM_CONFIG = {
    // 平台名称映射
    PLATFORM_NAMES: {
        tianyi: '天翼网盘',
        baidu: '百度网盘',
        aliyun: '阿里云盘',
        quark: '夸克网盘',
        lanzou: '蓝奏云',
        onedrive: 'OneDrive',
        googledrive: 'Google Drive'
    },

    // 平台颜色配置
    PLATFORM_COLORS: {
        tianyi: '#ff6b35',
        baidu: '#2196f3',
        aliyun: '#ff9800',
        quark: '#9c27b0',
        lanzou: '#4caf50',
        onedrive: '#0078d4',
        googledrive: '#4285f4',
        default: 'rgba(255, 255, 255, 0.2)'
    }
};

// 文件类型配置
export const FILE_TYPE_CONFIG = {
    // 文件图标映射
    FILE_ICONS: {
        pdf: { icon: '📄', color: '#dc3545' },
        doc: { icon: '📝', color: '#0d6efd' },
        docx: { icon: '📝', color: '#0d6efd' },
        xls: { icon: '📊', color: '#198754' },
        xlsx: { icon: '📊', color: '#198754' },
        ppt: { icon: '📊', color: '#fd7e14' },
        pptx: { icon: '📊', color: '#fd7e14' },
        zip: { icon: '🗜️', color: '#6f42c1' },
        rar: { icon: '🗜️', color: '#6f42c1' },
        '7z': { icon: '🗜️', color: '#6f42c1' },
        mp4: { icon: '🎬', color: '#e83e8c' },
        avi: { icon: '🎬', color: '#e83e8c' },
        mkv: { icon: '🎬', color: '#e83e8c' },
        mov: { icon: '🎬', color: '#e83e8c' },
        mp3: { icon: '🎵', color: '#20c997' },
        wav: { icon: '🎵', color: '#20c997' },
        flac: { icon: '🎵', color: '#20c997' },
        jpg: { icon: '🖼️', color: '#fd7e14' },
        jpeg: { icon: '🖼️', color: '#fd7e14' },
        png: { icon: '🖼️', color: '#fd7e14' },
        gif: { icon: '🖼️', color: '#fd7e14' },
        bmp: { icon: '🖼️', color: '#fd7e14' },
        txt: { icon: '📄', color: '#6c757d' },
        md: { icon: '📄', color: '#6c757d' },
        json: { icon: '📄', color: '#6c757d' },
        xml: { icon: '📄', color: '#6c757d' }
    },

    // 默认文件图标
    DEFAULT_ICON: { icon: '📄', color: '#6c757d' }
};

// UI 配置
export const UI_CONFIG = {
    // 动画持续时间（毫秒）
    ANIMATION_DURATION: 300,

    // 防抖延迟（毫秒）
    DEBOUNCE_DELAY: 300,

    // 节流延迟（毫秒）
    THROTTLE_DELAY: 100,

    // 加载状态显示延迟（毫秒）
    LOADING_DELAY: 200,

    // 错误消息显示时间（毫秒）
    ERROR_DISPLAY_TIME: 5000,

    // 成功消息显示时间（毫秒）
    SUCCESS_DISPLAY_TIME: 3000
};

// 搜索配置
export const SEARCH_CONFIG = {
    // 最小搜索关键词长度
    MIN_KEYWORD_LENGTH: 1,

    // 最大搜索关键词长度
    MAX_KEYWORD_LENGTH: 100,

    // 搜索历史最大数量
    MAX_SEARCH_HISTORY: 10,

    // 搜索建议最大数量
    MAX_SEARCH_SUGGESTIONS: 5,

    // 是否启用搜索历史
    ENABLE_SEARCH_HISTORY: true,

    // 是否启用搜索建议
    ENABLE_SEARCH_SUGGESTIONS: false
};

// 排序配置
export const SORT_CONFIG = {
    // 排序选项
    SORT_OPTIONS: [
        { value: 'default', label: '默认排序' },
        { value: 'newest', label: '最新优先' },
        { value: 'oldest', label: '最旧优先' },
        { value: 'name_asc', label: '名称升序' },
        { value: 'name_desc', label: '名称降序' }
    ],

    // 默认排序方式
    DEFAULT_SORT: 'default'
};

// 错误消息配置
export const ERROR_MESSAGES = {
    NETWORK_ERROR: '网络错误，请检查网络连接后重试',
    SEARCH_FAILED: '搜索失败，请稍后重试',
    INVALID_KEYWORD: '请输入有效的搜索关键词',
    KEYWORD_TOO_SHORT: '搜索关键词太短，请输入更多字符',
    KEYWORD_TOO_LONG: '搜索关键词太长，请缩短后重试',
    SERVER_ERROR: '服务器错误，请稍后重试',
    TIMEOUT_ERROR: '请求超时，请稍后重试',
    UNKNOWN_ERROR: '未知错误，请稍后重试'
};

// 成功消息配置
export const SUCCESS_MESSAGES = {
    SEARCH_SUCCESS: '搜索完成',
    COPY_SUCCESS: '已复制到剪贴板',
    SHARE_SUCCESS: '分享链接已生成'
};

// 本地存储键名
export const STORAGE_KEYS = {
    SEARCH_HISTORY: 'pansou_search_history',
    USER_PREFERENCES: 'pansou_user_preferences',
    CACHE_DATA: 'pansou_cache_data'
};

// 应用元信息
export const APP_INFO = {
    NAME: '网盘搜索',
    VERSION: '1.0.0',
    DESCRIPTION: '基于 Cloudflare Workers 的网盘搜索应用',
    AUTHOR: 'Developer',
    HOMEPAGE: 'https://github.com/your-repo/pansou-search'
};

// 开发环境配置
export const DEV_CONFIG = {
    // 是否启用调试模式
    DEBUG: false,

    // 是否启用性能监控
    ENABLE_PERFORMANCE_MONITORING: false,

    // 是否启用错误上报
    ENABLE_ERROR_REPORTING: false,

    // 日志级别
    LOG_LEVEL: 'info' // 'debug', 'info', 'warn', 'error'
};

// 导出所有配置的合并对象
export const CONFIG = {
    API: API_CONFIG,
    PAGINATION: PAGINATION_CONFIG,
    CACHE: CACHE_CONFIG,
    PLATFORM: PLATFORM_CONFIG,
    FILE_TYPE: FILE_TYPE_CONFIG,
    UI: UI_CONFIG,
    SEARCH: SEARCH_CONFIG,
    SORT: SORT_CONFIG,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES,
    STORAGE_KEYS,
    APP_INFO,
    DEV: DEV_CONFIG
};
