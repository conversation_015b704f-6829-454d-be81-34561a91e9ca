import { 
    XSSProtection,
    sanitizeHTML,
    sanitizeText,
    sanitizeSearchKeyword,
    sanitizeFilename,
    isUrlSafe,
    detectXSS
} from '../src/scripts/utils/xss-protection.js';

/**
 * XSS防护测试
 */
function runXSSProtectionTests() {
    console.log('开始运行XSS防护测试...');
    
    // 测试HTML清理
    testHTMLSanitization();
    
    // 测试文本清理
    testTextSanitization();
    
    // 测试搜索关键词清理
    testSearchKeywordSanitization();
    
    // 测试文件名清理
    testFilenameSanitization();
    
    // 测试URL安全检查
    testUrlSafety();
    
    // 测试XSS检测
    testXSSDetection();
    
    console.log('所有XSS防护测试完成！');
}

function testHTMLSanitization() {
    console.log('测试HTML清理...');
    
    // 测试脚本标签移除
    const scriptHTML = '<div>正常内容</div><script>alert("xss")</script><p>更多内容</p>';
    const cleanScript = sanitizeHTML(scriptHTML);
    
    if (!cleanScript.includes('<script>')) {
        console.log('✓ 成功移除script标签');
    } else {
        console.error('✗ 未能移除script标签');
    }
    
    // 测试iframe标签移除
    const iframeHTML = '<div>内容</div><iframe src="javascript:alert(1)"></iframe>';
    const cleanIframe = sanitizeHTML(iframeHTML);
    
    if (!cleanIframe.includes('<iframe>')) {
        console.log('✓ 成功移除iframe标签');
    } else {
        console.error('✗ 未能移除iframe标签');
    }
    
    // 测试事件处理器移除
    const eventHTML = '<div onclick="alert(1)">点击我</div>';
    const cleanEvent = sanitizeHTML(eventHTML);
    
    if (!cleanEvent.includes('onclick')) {
        console.log('✓ 成功移除事件处理器');
    } else {
        console.error('✗ 未能移除事件处理器');
    }
    
    // 测试JavaScript协议移除
    const jsProtocol = '<a href="javascript:alert(1)">链接</a>';
    const cleanJS = sanitizeHTML(jsProtocol);
    
    if (!cleanJS.includes('javascript:')) {
        console.log('✓ 成功移除JavaScript协议');
    } else {
        console.error('✗ 未能移除JavaScript协议');
    }
    
    // 测试允许的标签保留
    const allowedHTML = '<p>段落</p><strong>粗体</strong><em>斜体</em>';
    const cleanAllowed = sanitizeHTML(allowedHTML, {
        allowedTags: ['p', 'strong', 'em']
    });
    
    if (cleanAllowed.includes('<p>') && cleanAllowed.includes('<strong>')) {
        console.log('✓ 正确保留允许的标签');
    } else {
        console.error('✗ 未能正确保留允许的标签');
    }
}

function testTextSanitization() {
    console.log('测试文本清理...');
    
    // 测试HTML标签移除
    const htmlText = '<p>这是<strong>文本</strong>内容</p>';
    const cleanText = sanitizeText(htmlText);
    
    if (!cleanText.includes('<') && !cleanText.includes('>')) {
        console.log('✓ 成功移除HTML标签');
    } else {
        console.error('✗ 未能移除HTML标签');
    }
    
    // 测试HTML实体解码
    const entityText = '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;';
    const decodedText = sanitizeText(entityText);
    
    if (decodedText.includes('<') && decodedText.includes('>')) {
        console.log('✓ 成功解码HTML实体');
    } else {
        console.error('✗ 未能解码HTML实体');
    }
}

function testSearchKeywordSanitization() {
    console.log('测试搜索关键词清理...');
    
    // 测试正常关键词
    const normalKeyword = '测试关键词';
    const cleanNormal = sanitizeSearchKeyword(normalKeyword);
    
    if (cleanNormal === normalKeyword) {
        console.log('✓ 正常关键词保持不变');
    } else {
        console.error('✗ 正常关键词被错误修改');
    }
    
    // 测试包含HTML的关键词
    const htmlKeyword = '<script>alert("xss")</script>搜索';
    const cleanHTML = sanitizeSearchKeyword(htmlKeyword);
    
    if (!cleanHTML.includes('<script>')) {
        console.log('✓ 成功清理HTML关键词');
    } else {
        console.error('✗ 未能清理HTML关键词');
    }
    
    // 测试过长关键词
    const longKeyword = 'a'.repeat(150);
    const cleanLong = sanitizeSearchKeyword(longKeyword);
    
    if (cleanLong.length <= 100) {
        console.log('✓ 成功限制关键词长度');
    } else {
        console.error('✗ 未能限制关键词长度');
    }
}

function testFilenameSanitization() {
    console.log('测试文件名清理...');
    
    // 测试正常文件名
    const normalFilename = 'document.pdf';
    const cleanNormal = sanitizeFilename(normalFilename);
    
    if (cleanNormal === normalFilename) {
        console.log('✓ 正常文件名保持不变');
    } else {
        console.error('✗ 正常文件名被错误修改');
    }
    
    // 测试包含危险字符的文件名
    const dangerousFilename = 'file<script>.exe';
    const cleanDangerous = sanitizeFilename(dangerousFilename);
    
    if (!cleanDangerous.includes('<') && !cleanDangerous.includes('>')) {
        console.log('✓ 成功清理危险字符');
    } else {
        console.error('✗ 未能清理危险字符');
    }
}

function testUrlSafety() {
    console.log('测试URL安全检查...');
    
    // 测试安全URL
    const safeUrls = [
        'https://example.com',
        'http://localhost:8080',
        '/relative/path',
        'mailto:<EMAIL>'
    ];
    
    safeUrls.forEach(url => {
        if (isUrlSafe(url)) {
            console.log(`✓ 正确识别安全URL: ${url}`);
        } else {
            console.error(`✗ 错误拒绝安全URL: ${url}`);
        }
    });
    
    // 测试危险URL
    const dangerousUrls = [
        'javascript:alert(1)',
        'data:text/html,<script>alert(1)</script>',
        'vbscript:msgbox(1)',
        'file:///etc/passwd'
    ];
    
    dangerousUrls.forEach(url => {
        if (!isUrlSafe(url)) {
            console.log(`✓ 正确识别危险URL: ${url}`);
        } else {
            console.error(`✗ 错误允许危险URL: ${url}`);
        }
    });
}

function testXSSDetection() {
    console.log('测试XSS检测...');
    
    // 测试正常内容
    const normalContent = '这是正常的文本内容';
    if (!detectXSS(normalContent)) {
        console.log('✓ 正确识别正常内容');
    } else {
        console.error('✗ 错误标记正常内容为XSS');
    }
    
    // 测试XSS攻击
    const xssAttacks = [
        '<script>alert("xss")</script>',
        '<iframe src="javascript:alert(1)"></iframe>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert(1)',
        '<object data="data:text/html,<script>alert(1)</script>"></object>'
    ];
    
    xssAttacks.forEach(attack => {
        if (detectXSS(attack)) {
            console.log(`✓ 正确检测XSS攻击: ${attack.substring(0, 30)}...`);
        } else {
            console.error(`✗ 未能检测XSS攻击: ${attack.substring(0, 30)}...`);
        }
    });
}

// 运行测试
runXSSProtectionTests();
