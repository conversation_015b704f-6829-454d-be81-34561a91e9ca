#!/usr/bin/env node

/**
 * 性能测试脚本
 * 运行 Lighthouse CI 并生成性能报告
 */

import { execSync, spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class PerformanceTester {
    constructor() {
        this.results = {
            lighthouse: null,
            timestamp: new Date().toISOString()
        };
        this.serverProcess = null;
    }

    /**
     * 运行命令并捕获输出
     */
    runCommand(command, options = {}) {
        try {
            const output = execSync(command, {
                encoding: 'utf8',
                stdio: 'pipe',
                ...options
            });
            return { success: true, output };
        } catch (error) {
            return { 
                success: false, 
                output: error.stdout || error.message,
                error: error.stderr || error.message
            };
        }
    }

    /**
     * 启动开发服务器
     */
    async startDevServer() {
        console.log('🚀 启动开发服务器...');
        
        return new Promise((resolve, reject) => {
            this.serverProcess = spawn('npm', ['run', 'dev'], {
                stdio: 'pipe',
                shell: true
            });

            let output = '';
            
            this.serverProcess.stdout.on('data', (data) => {
                output += data.toString();
                console.log(data.toString().trim());
                
                // 检查服务器是否已启动
                if (output.includes('Ready on') || output.includes('localhost:8787')) {
                    console.log('✅ 开发服务器已启动');
                    resolve();
                }
            });

            this.serverProcess.stderr.on('data', (data) => {
                console.error(data.toString().trim());
            });

            this.serverProcess.on('error', (error) => {
                console.error('❌ 启动开发服务器失败:', error);
                reject(error);
            });

            // 30秒超时
            setTimeout(() => {
                if (this.serverProcess && !this.serverProcess.killed) {
                    console.log('⚠️ 服务器启动超时，继续执行测试...');
                    resolve();
                }
            }, 30000);
        });
    }

    /**
     * 停止开发服务器
     */
    stopDevServer() {
        if (this.serverProcess && !this.serverProcess.killed) {
            console.log('🛑 停止开发服务器...');
            this.serverProcess.kill('SIGTERM');
            
            // 强制杀死进程
            setTimeout(() => {
                if (this.serverProcess && !this.serverProcess.killed) {
                    this.serverProcess.kill('SIGKILL');
                }
            }, 5000);
        }
    }

    /**
     * 运行 Lighthouse CI
     */
    async runLighthouseCI() {
        console.log('🔍 运行 Lighthouse CI...');
        
        const result = this.runCommand('npx lhci autorun --config=lighthouserc.js');
        
        if (result.success) {
            console.log('✅ Lighthouse CI 执行成功');
            
            // 尝试读取 Lighthouse 结果
            try {
                const lhciDir = path.join(process.cwd(), '.lighthouseci');
                if (fs.existsSync(lhciDir)) {
                    const files = fs.readdirSync(lhciDir);
                    const reportFiles = files.filter(f => f.endsWith('.json'));
                    
                    if (reportFiles.length > 0) {
                        const reportPath = path.join(lhciDir, reportFiles[0]);
                        const reportData = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
                        
                        this.results.lighthouse = {
                            success: true,
                            scores: this.extractScores(reportData),
                            metrics: this.extractMetrics(reportData)
                        };
                    }
                }
            } catch (error) {
                console.log('⚠️ 无法解析 Lighthouse 结果:', error.message);
                this.results.lighthouse = { success: true, output: result.output };
            }
        } else {
            console.log('❌ Lighthouse CI 执行失败:', result.error);
            this.results.lighthouse = { success: false, error: result.error };
        }
    }

    /**
     * 提取 Lighthouse 评分
     */
    extractScores(reportData) {
        const categories = reportData.categories || {};
        const scores = {};
        
        Object.entries(categories).forEach(([key, category]) => {
            scores[key] = {
                score: Math.round((category.score || 0) * 100),
                title: category.title
            };
        });
        
        return scores;
    }

    /**
     * 提取 Lighthouse 指标
     */
    extractMetrics(reportData) {
        const audits = reportData.audits || {};
        const metrics = {};
        
        const keyMetrics = [
            'first-contentful-paint',
            'largest-contentful-paint',
            'cumulative-layout-shift',
            'total-blocking-time',
            'speed-index'
        ];
        
        keyMetrics.forEach(metric => {
            if (audits[metric]) {
                metrics[metric] = {
                    value: audits[metric].numericValue,
                    displayValue: audits[metric].displayValue,
                    score: Math.round((audits[metric].score || 0) * 100)
                };
            }
        });
        
        return metrics;
    }

    /**
     * 生成性能报告
     */
    generateReport() {
        console.log('\n📊 生成性能测试报告...');
        
        const report = {
            timestamp: this.results.timestamp,
            summary: {
                lighthouse: this.results.lighthouse?.success ? 'PASS' : 'FAIL'
            },
            details: this.results
        };
        
        // 保存报告到文件
        const reportPath = path.join(process.cwd(), 'performance-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`📄 报告已保存到: ${reportPath}`);
        
        // 打印性能评分
        if (this.results.lighthouse?.scores) {
            console.log('\n📋 Lighthouse 评分:');
            Object.entries(this.results.lighthouse.scores).forEach(([category, data]) => {
                const icon = data.score >= 80 ? '✅' : data.score >= 60 ? '⚠️' : '❌';
                console.log(`  ${icon} ${data.title}: ${data.score}/100`);
            });
        }
        
        // 打印核心指标
        if (this.results.lighthouse?.metrics) {
            console.log('\n📈 核心 Web 指标:');
            Object.entries(this.results.lighthouse.metrics).forEach(([metric, data]) => {
                const icon = data.score >= 80 ? '✅' : data.score >= 60 ? '⚠️' : '❌';
                console.log(`  ${icon} ${metric}: ${data.displayValue} (${data.score}/100)`);
            });
        }
        
        // 计算总体状态
        const allPassed = this.results.lighthouse?.success;
        
        if (allPassed) {
            console.log('\n🎉 性能测试完成！');
            return 0;
        } else {
            console.log('\n⚠️ 性能测试未通过，请查看详细报告');
            return 1;
        }
    }

    /**
     * 运行所有性能测试
     */
    async runAll() {
        console.log('🚀 开始性能测试...\n');
        
        try {
            // 启动开发服务器
            await this.startDevServer();
            
            // 等待服务器稳定
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 运行 Lighthouse CI
            await this.runLighthouseCI();
            
        } finally {
            // 确保停止服务器
            this.stopDevServer();
        }
        
        return this.generateReport();
    }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new PerformanceTester();
    
    // 处理进程退出
    process.on('SIGINT', () => {
        console.log('\n🛑 收到中断信号，正在清理...');
        tester.stopDevServer();
        process.exit(0);
    });
    
    tester.runAll()
        .then(exitCode => {
            process.exit(exitCode);
        })
        .catch(error => {
            console.error('❌ 性能测试失败:', error);
            tester.stopDevServer();
            process.exit(1);
        });
}

export default PerformanceTester;
