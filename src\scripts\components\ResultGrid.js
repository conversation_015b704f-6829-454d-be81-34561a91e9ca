/**
 * 结果网格组件
 * 负责渲染搜索结果和处理结果显示
 */
export class ResultGrid {
    constructor() {
        this.container = document.getElementById('resultsGrid');
        this.resultsSection = document.getElementById('resultsSection');
        this.resultsCount = document.getElementById('resultsCount');
        this.sortSelect = document.getElementById('sortSelect');
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // 视图切换事件
        const viewButtons = document.querySelectorAll('.view-btn');
        viewButtons.forEach(btn => {
            btn.addEventListener('click', e => {
                viewButtons.forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                // 这里可以添加视图切换逻辑
            });
        });
    }

    /**
     * 渲染搜索结果
     * @param {Array} results - 搜索结果数组
     * @param {number} totalCount - 总结果数量
     */
    render(results, totalCount) {
        if (!this.container || !this.resultsSection || !this.resultsCount) {
            return;
        }

        // 显示结果区域
        this.resultsSection.style.display = 'block';

        // 更新结果计数
        this.resultsCount.textContent = `找到 ${totalCount} 个文件`;

        // 渲染结果卡片
        this.container.innerHTML = results.map(item => this.createResultCard(item)).join('');
    }

    /**
     * 创建结果卡片HTML
     * @param {Object} item - 结果项数据
     * @returns {string} HTML字符串
     */
    createResultCard(item) {
        const icon = this.getFileIcon(item.note);
        const platformClass = `platform-${item.platform}`;
        const date = item.datetime ? new Date(item.datetime).toLocaleDateString() : '未知';

        return `
            <div class="result-card">
                <div class="card-header">
                    <div class="file-icon" style="background: ${icon.color};">${icon.icon}</div>
                    <div class="file-title">${item.note || '未命名文件'}</div>
                </div>
                <div class="file-meta">
                    <span>${date}</span>
                    <span class="platform-badge ${platformClass}">${item.platformName}</span>
                </div>
                <div class="file-actions">
                    <a href="${item.url}" target="_blank" class="action-btn">打开链接</a>
                    ${item.password ? `<span class="action-btn">密码: ${item.password}</span>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 根据文件名获取文件图标
     * @param {string} filename - 文件名
     * @returns {Object} 包含图标和颜色的对象
     */
    getFileIcon(filename) {
        if (!filename) {
            return { icon: '📄', color: '#6c757d' };
        }

        const ext = filename.split('.').pop()?.toLowerCase();
        const icons = {
            pdf: { icon: '📄', color: '#dc3545' },
            doc: { icon: '📝', color: '#0d6efd' },
            docx: { icon: '📝', color: '#0d6efd' },
            xls: { icon: '📊', color: '#198754' },
            xlsx: { icon: '📊', color: '#198754' },
            ppt: { icon: '📊', color: '#fd7e14' },
            pptx: { icon: '📊', color: '#fd7e14' },
            zip: { icon: '🗜️', color: '#6f42c1' },
            rar: { icon: '🗜️', color: '#6f42c1' },
            mp4: { icon: '🎬', color: '#e83e8c' },
            avi: { icon: '🎬', color: '#e83e8c' },
            mp3: { icon: '🎵', color: '#20c997' },
            jpg: { icon: '🖼️', color: '#fd7e14' },
            png: { icon: '🖼️', color: '#fd7e14' },
            txt: { icon: '📄', color: '#6c757d' }
        };

        return icons[ext] || { icon: '📄', color: '#6c757d' };
    }

    /**
     * 清空结果显示
     */
    clear() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        if (this.resultsSection) {
            this.resultsSection.style.display = 'none';
        }
    }

    /**
     * 获取排序选择器的值
     * @returns {string} 排序类型
     */
    getSortType() {
        return this.sortSelect ? this.sortSelect.value : 'default';
    }

    /**
     * 设置排序选择器的值
     * @param {string} sortType - 排序类型
     */
    setSortType(sortType) {
        if (this.sortSelect) {
            this.sortSelect.value = sortType;
        }
    }

    /**
     * 绑定排序变化事件
     * @param {Function} onSortChange - 排序变化回调函数
     */
    onSortChange(onSortChange) {
        if (this.sortSelect) {
            this.sortSelect.addEventListener('change', e => {
                onSortChange(e.target.value);
            });
        }
    }
}
