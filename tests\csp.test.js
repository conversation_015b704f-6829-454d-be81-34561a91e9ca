import { 
    generateCSPHeader,
    getSecurityHeaders,
    CSP_CONFIG 
} from '../src/config/security.js';

/**
 * CSP配置测试
 */
function runCSPTests() {
    console.log('开始运行CSP配置测试...');
    
    // 测试CSP头部生成
    testCSPGeneration();
    
    // 测试环境特定的CSP配置
    testEnvironmentSpecificCSP();
    
    // 测试安全头部集成
    testSecurityHeadersIntegration();
    
    // 测试CSP指令覆盖
    testCSPDirectiveCoverage();
    
    console.log('所有CSP配置测试完成！');
}

function testCSPGeneration() {
    console.log('测试CSP头部生成...');
    
    // 测试生产环境CSP
    const productionCSP = generateCSPHeader('production');
    
    if (productionCSP && productionCSP.length > 0) {
        console.log('✓ 成功生成生产环境CSP头部');
    } else {
        console.error('✗ 未能生成生产环境CSP头部');
    }
    
    // 测试开发环境CSP
    const developmentCSP = generateCSPHeader('development');
    
    if (developmentCSP && developmentCSP.length > 0) {
        console.log('✓ 成功生成开发环境CSP头部');
    } else {
        console.error('✗ 未能生成开发环境CSP头部');
    }
    
    // 验证开发环境包含unsafe-eval
    if (developmentCSP.includes("'unsafe-eval'")) {
        console.log('✓ 开发环境CSP正确包含unsafe-eval');
    } else {
        console.error('✗ 开发环境CSP应该包含unsafe-eval');
    }
    
    // 验证生产环境不包含unsafe-eval
    if (!productionCSP.includes("'unsafe-eval'")) {
        console.log('✓ 生产环境CSP正确排除unsafe-eval');
    } else {
        console.error('✗ 生产环境CSP不应该包含unsafe-eval');
    }
}

function testEnvironmentSpecificCSP() {
    console.log('测试环境特定的CSP配置...');
    
    const environments = ['development', 'staging', 'production'];
    
    environments.forEach(env => {
        const csp = generateCSPHeader(env);
        
        // 所有环境都应该有基本的CSP指令
        const requiredDirectives = [
            'default-src',
            'script-src',
            'style-src',
            'img-src',
            'connect-src'
        ];
        
        requiredDirectives.forEach(directive => {
            if (csp.includes(directive)) {
                console.log(`✓ ${env}环境CSP包含${directive}指令`);
            } else {
                console.error(`✗ ${env}环境CSP缺少${directive}指令`);
            }
        });
        
        // 检查安全指令
        if (csp.includes('object-src') && csp.includes("'none'")) {
            console.log(`✓ ${env}环境CSP正确禁用object-src`);
        } else {
            console.error(`✗ ${env}环境CSP应该禁用object-src`);
        }
        
        if (csp.includes('base-uri') && csp.includes("'self'")) {
            console.log(`✓ ${env}环境CSP正确限制base-uri`);
        } else {
            console.error(`✗ ${env}环境CSP应该限制base-uri`);
        }
    });
}

function testSecurityHeadersIntegration() {
    console.log('测试安全头部集成...');
    
    const origin = 'https://pansou.104078.xyz';
    
    // 测试生产环境安全头部
    const prodHeaders = getSecurityHeaders(origin, 'production');
    
    if (prodHeaders['Content-Security-Policy']) {
        console.log('✓ 安全头部包含CSP');
    } else {
        console.error('✗ 安全头部缺少CSP');
    }
    
    if (prodHeaders['X-Content-Type-Options'] === 'nosniff') {
        console.log('✓ 安全头部包含正确的X-Content-Type-Options');
    } else {
        console.error('✗ 安全头部缺少或错误的X-Content-Type-Options');
    }
    
    if (prodHeaders['X-Frame-Options'] === 'DENY') {
        console.log('✓ 安全头部包含正确的X-Frame-Options');
    } else {
        console.error('✗ 安全头部缺少或错误的X-Frame-Options');
    }
    
    if (prodHeaders['Strict-Transport-Security']) {
        console.log('✓ 生产环境包含HSTS头部');
    } else {
        console.error('✗ 生产环境应该包含HSTS头部');
    }
    
    // 测试开发环境安全头部
    const devHeaders = getSecurityHeaders(origin, 'development');
    
    if (!devHeaders['Strict-Transport-Security']) {
        console.log('✓ 开发环境正确移除HSTS头部');
    } else {
        console.error('✗ 开发环境不应该包含HSTS头部');
    }
}

function testCSPDirectiveCoverage() {
    console.log('测试CSP指令覆盖...');
    
    const csp = generateCSPHeader('production');
    
    // 检查关键安全指令
    const securityDirectives = [
        { directive: 'default-src', expected: "'self'" },
        { directive: 'object-src', expected: "'none'" },
        { directive: 'base-uri', expected: "'self'" },
        { directive: 'form-action', expected: "'self'" },
        { directive: 'frame-ancestors', expected: "'none'" }
    ];
    
    securityDirectives.forEach(({ directive, expected }) => {
        if (csp.includes(directive) && csp.includes(expected)) {
            console.log(`✓ CSP正确配置${directive}为${expected}`);
        } else {
            console.error(`✗ CSP未正确配置${directive}为${expected}`);
        }
    });
    
    // 检查资源加载指令
    const resourceDirectives = [
        'script-src',
        'style-src',
        'img-src',
        'font-src',
        'connect-src',
        'media-src'
    ];
    
    resourceDirectives.forEach(directive => {
        if (csp.includes(directive)) {
            console.log(`✓ CSP包含资源指令: ${directive}`);
        } else {
            console.error(`✗ CSP缺少资源指令: ${directive}`);
        }
    });
    
    // 检查是否包含危险配置
    const dangerousConfigs = [
        "'unsafe-eval'", // 在生产环境中不应该有
        "*" // 通配符应该谨慎使用
    ];
    
    const prodCSP = generateCSPHeader('production');
    dangerousConfigs.forEach(config => {
        if (config === "'unsafe-eval'" && !prodCSP.includes(config)) {
            console.log(`✓ 生产环境CSP正确排除危险配置: ${config}`);
        } else if (config === "*" && !prodCSP.includes(`${config} `)) {
            console.log(`✓ 生产环境CSP正确避免通配符: ${config}`);
        } else if (config === "'unsafe-eval'") {
            console.error(`✗ 生产环境CSP不应该包含: ${config}`);
        }
    });
}

// 运行测试
runCSPTests();
