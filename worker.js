// Cloudflare Workers 网盘搜索应用
export default {
    async fetch(request, _env, _ctx) {
        const url = new URL(request.url);

        // 处理 CORS 预检请求
        if (request.method === 'OPTIONS') {
            return new Response(null, {
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type'
                }
            });
        }

        // 处理搜索 API 请求
        if (url.pathname === '/api/search') {
            return handleSearchAPI(request);
        }

        // 处理静态资源请求
        if (url.pathname === '/') {
            return new Response(HTML_CONTENT, {
                headers: { 'Content-Type': 'text/html; charset=utf-8' }
            });
        }

        return new Response('Not Found', { status: 404 });
    }
};

// 处理搜索 API 请求
async function handleSearchAPI(request) {
    try {
        const url = new URL(request.url);
        const kw = url.searchParams.get('kw');

        if (!kw) {
            return new Response(JSON.stringify({ error: '缺少搜索关键词' }), {
                status: 400,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            });
        }

        // 构建搜索 API URL
        const searchUrl = new URL('https://pansou.252035.xyz/api/search');
        searchUrl.searchParams.set('kw', kw);
        searchUrl.searchParams.set('refresh', url.searchParams.get('refresh') || 'false');
        searchUrl.searchParams.set('res', url.searchParams.get('res') || 'merge');
        searchUrl.searchParams.set('src', url.searchParams.get('src') || 'all');
        searchUrl.searchParams.set(
            'plugins',
            url.searchParams.get('plugins') || 'pansearch,qupansou,panta,pan666,hunhepan,jikepan'
        );

        // 调用搜索 API
        const response = await fetch(searchUrl.toString());
        const data = await response.text();

        return new Response(data, {
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
    } catch (error) {
        console.error('Search API error:', error);
        return new Response(JSON.stringify({ error: '搜索请求失败' }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
    }
}

// HTML 内容
const HTML_CONTENT = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网盘搜索 - File Storage</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 16px;
            opacity: 0.95;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.8;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .search-section {
            margin-bottom: 40px;
        }
        
        .search-box {
            position: relative;
            max-width: 800px;
            margin: 0 auto 30px;
        }
        
        .search-input {
            width: 100%;
            padding: 16px 60px 16px 20px;
            font-size: 1rem;
            border: none;
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            color: white;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .search-input:focus {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) scale(1.1);
        }
        
        .filters {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 8px 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .filter-btn:hover, .filter-btn.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .filter-btn .count {
            margin-left: 5px;
            opacity: 0.7;
            font-size: 0.8rem;
        }

        .filters.loading .filter-btn:not([data-type="all"]) {
            opacity: 0.5;
            pointer-events: none;
        }
        
        .results-section {
            margin-top: 40px;
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .results-count {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .results-controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .sort-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sort-select {
            padding: 6px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
        }

        .sort-select:hover, .sort-select:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .sort-select option {
            background: #333;
            color: white;
        }

        .view-toggle {
            display: flex;
            gap: 10px;
        }
        
        .view-btn {
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .view-btn.active {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .result-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .result-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .file-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 1.2rem;
        }
        
        .file-title {
            font-weight: 500;
            font-size: 1rem;
            line-height: 1.4;
            flex: 1;
        }
        
        .file-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            opacity: 0.8;
            margin-bottom: 12px;
        }
        
        .file-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            padding: 6px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 40px;
        }
        
        .page-btn {
            padding: 10px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .page-btn:hover, .page-btn.active {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.1rem;
        }
        
        .error {
            text-align: center;
            padding: 40px;
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 12px;
            margin: 20px 0;
        }
        
        .empty {
            text-align: center;
            padding: 60px 20px;
            opacity: 0.7;
        }
        
        .empty h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .platform-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
            margin-left: 8px;
        }
        
        .platform-tianyi { background: #ff6b35; }
        .platform-baidu { background: #2196f3; }
        .platform-aliyun { background: #ff9800; }
        .platform-default { background: rgba(255, 255, 255, 0.2); }
        
        @media (max-width: 768px) {
            .container { padding: 20px 15px; }
            .header h1 { font-size: 2rem; }
            .results-grid { grid-template-columns: 1fr; }
            .filters { justify-content: center; }
            .results-header { flex-direction: column; gap: 15px; }
            .results-controls {
                flex-direction: column;
                gap: 15px;
                width: 100%;
            }
            .sort-controls {
                justify-content: center;
            }
            .view-toggle {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>File Storage</h1>
            <p>发现、整理和管理您的文件，享受直观而强大的搜索体验</p>
        </div>
        
        <div class="search-section">
            <div class="search-box">
                <input type="text" class="search-input" placeholder="搜索文件、文件夹和内容..." id="searchInput">
                <button class="search-btn" id="searchBtn">🔍</button>
            </div>
            
            <div class="filters" id="dynamicFilters">
                <button class="filter-btn active" data-type="all">所有文件</button>
            </div>
        </div>
        
        <div class="results-section" id="resultsSection" style="display: none;">
            <div class="results-header">
                <div class="results-count" id="resultsCount">找到 0 个文件</div>
                <div class="results-controls">
                    <div class="sort-controls">
                        <select class="sort-select" id="sortSelect">
                            <option value="default">默认排序</option>
                            <option value="newest">最新优先</option>
                            <option value="oldest">最旧优先</option>
                        </select>
                    </div>
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid">⊞</button>
                        <button class="view-btn" data-view="list">☰</button>
                    </div>
                </div>
            </div>
            
            <div class="results-grid" id="resultsGrid"></div>
            
            <div class="pagination" id="pagination"></div>
        </div>
        
        <div class="loading" id="loading" style="display: none;">
            <div>🔍 正在搜索...</div>
        </div>
        
        <div class="error" id="error" style="display: none;"></div>
        
        <div class="empty" id="empty">
            <h3>开始搜索</h3>
            <p>输入关键词来搜索网盘资源</p>
        </div>
    </div>

    <script>
        class PanSearchApp {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 12;
                this.currentResults = [];
                this.currentFilter = 'all';
                this.currentSort = 'default';
                this.availablePlatforms = new Map();
                this.init();
            }
            
            init() {
                this.bindEvents();
            }
            
            bindEvents() {
                // 搜索按钮点击
                document.getElementById('searchBtn').addEventListener('click', () => this.search());

                // 搜索框回车
                document.getElementById('searchInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.search();
                });

                // 排序选择
                document.getElementById('sortSelect').addEventListener('change', (e) => {
                    this.currentSort = e.target.value;
                    this.currentPage = 1; // 重置到第一页
                    this.renderResults();
                });

                // 视图切换
                document.querySelectorAll('.view-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                    });
                });
            }
            
            async search() {
                const keyword = document.getElementById('searchInput').value.trim();
                if (!keyword) return;
                
                this.showLoading();
                
                try {
                    const response = await fetch('/api/search?kw=' + encodeURIComponent(keyword));
                    const data = await response.json();
                    
                    if (data.code === 0) {
                        this.currentResults = this.processResults(data.data);
                        this.generateDynamicFilters();
                        this.currentPage = 1;
                        this.currentFilter = 'all'; // 重置筛选
                        this.currentSort = 'default'; // 重置排序
                        document.getElementById('sortSelect').value = 'default';
                        this.renderResults();
                    } else {
                        this.showError('\u641c\u7d22\u5931\u8d25\uff1a' + (data.message || '\u672a\u77e5\u9519\u8bef'));
                    }
                } catch (error) {
                    this.showError('\u7f51\u7edc\u9519\u8bef\uff0c\u8bf7\u7a0d\u540e\u91cd\u8bd5');
                }
            }
            
            processResults(data) {
                const results = [];
                this.availablePlatforms.clear();

                if (data.merged_by_type) {
                    Object.entries(data.merged_by_type).forEach(([platform, items]) => {
                        // 记录平台和数量
                        this.availablePlatforms.set(platform, {
                            name: this.getPlatformName(platform),
                            count: items.length
                        });

                        items.forEach(item => {
                            results.push({
                                ...item,
                                platform: platform,
                                platformName: this.getPlatformName(platform)
                            });
                        });
                    });
                }

                return results;
            }
            
            getPlatformName(platform) {
                const names = {
                    'tianyi': '天翼网盘',
                    'baidu': '百度网盘',
                    'aliyun': '阿里云盘',
                    'quark': '夸克网盘',
                    'lanzou': '蓝奏云',
                    'onedrive': 'OneDrive',
                    'googledrive': 'Google Drive'
                };
                return names[platform] || platform;
            }

            generateDynamicFilters() {
                const filtersContainer = document.getElementById('dynamicFilters');

                // 清除现有的动态筛选按钮，保留"所有文件"按钮
                const allFilesBtn = filtersContainer.querySelector('[data-type="all"]');
                filtersContainer.innerHTML = '';
                filtersContainer.appendChild(allFilesBtn);

                // 更新"所有文件"按钮的计数
                const totalCount = this.currentResults.length;
                allFilesBtn.innerHTML = '\u6240\u6709\u6587\u4ef6 <span class="count">(' + totalCount + ')</span>';

                // 生成动态筛选按钮
                const self = this;
                this.availablePlatforms.forEach(function(info, platform) {
                    const btn = document.createElement('button');
                    btn.className = 'filter-btn';
                    btn.dataset.type = platform;
                    btn.innerHTML = info.name + ' <span class="count">(' + info.count + ')</span>';

                    // 添加点击事件
                    btn.addEventListener('click', function(e) {
                        document.querySelectorAll('.filter-btn').forEach(function(b) {
                            b.classList.remove('active');
                        });
                        e.target.classList.add('active');
                        self.currentFilter = e.target.dataset.type;
                        self.currentPage = 1; // 重置到第一页
                        self.renderResults();
                    });

                    filtersContainer.appendChild(btn);
                });

                // 重新激活当前筛选
                const activeBtn = filtersContainer.querySelector('[data-type="' + this.currentFilter + '"]');
                if (activeBtn) {
                    document.querySelectorAll('.filter-btn').forEach(function(b) {
                        b.classList.remove('active');
                    });
                    activeBtn.classList.add('active');
                }
            }
            
            getFilteredResults() {
                let results;
                if (this.currentFilter === 'all') {
                    results = [...this.currentResults];
                } else {
                    results = this.currentResults.filter(item => item.platform === this.currentFilter);
                }

                return this.sortResults(results);
            }

            sortResults(results) {
                if (this.currentSort === 'default') {
                    return results;
                }

                return results.sort((a, b) => {
                    const dateA = a.datetime ? new Date(a.datetime) : new Date(0);
                    const dateB = b.datetime ? new Date(b.datetime) : new Date(0);

                    if (this.currentSort === 'newest') {
                        return dateB - dateA; // 最新优先
                    } else if (this.currentSort === 'oldest') {
                        return dateA - dateB; // 最旧优先
                    }

                    return 0;
                });
            }
            
            renderResults() {
                const filteredResults = this.getFilteredResults();
                const startIndex = (this.currentPage - 1) * this.pageSize;
                const endIndex = startIndex + this.pageSize;
                const pageResults = filteredResults.slice(startIndex, endIndex);
                
                this.hideAll();
                document.getElementById('resultsSection').style.display = 'block';
                
                // 更新结果计数
                document.getElementById('resultsCount').textContent = '\u627e\u5230 ' + filteredResults.length + ' \u4e2a\u6587\u4ef6';
                
                // 渲染结果
                const grid = document.getElementById('resultsGrid');
                grid.innerHTML = pageResults.map(item => this.createResultCard(item)).join('');
                
                // 渲染分页
                this.renderPagination(filteredResults.length);
            }
            
            createResultCard(item) {
                const icon = this.getFileIcon(item.note);
                const platformClass = 'platform-' + item.platform;
                const date = item.datetime ? new Date(item.datetime).toLocaleDateString() : '\u672a\u77e5';

                return '<div class="result-card">' +
                    '<div class="card-header">' +
                        '<div class="file-icon" style="background: ' + icon.color + ';">' + icon.icon + '</div>' +
                        '<div class="file-title">' + (item.note || '\u672a\u547d\u540d\u6587\u4ef6') + '</div>' +
                    '</div>' +
                    '<div class="file-meta">' +
                        '<span>' + date + '</span>' +
                        '<span class="platform-badge ' + platformClass + '">' + item.platformName + '</span>' +
                    '</div>' +
                    '<div class="file-actions">' +
                        '<a href="' + item.url + '" target="_blank" class="action-btn">\u6253\u5f00\u94fe\u63a5</a>' +
                        (item.password ? '<span class="action-btn">\u5bc6\u7801: ' + item.password + '</span>' : '') +
                    '</div>' +
                '</div>';
            }
            
            getFileIcon(filename) {
                if (!filename) return { icon: '📄', color: '#6c757d' };
                
                const ext = filename.split('.').pop()?.toLowerCase();
                const icons = {
                    'pdf': { icon: '📄', color: '#dc3545' },
                    'doc': { icon: '📝', color: '#0d6efd' },
                    'docx': { icon: '📝', color: '#0d6efd' },
                    'xls': { icon: '📊', color: '#198754' },
                    'xlsx': { icon: '📊', color: '#198754' },
                    'ppt': { icon: '📊', color: '#fd7e14' },
                    'pptx': { icon: '📊', color: '#fd7e14' },
                    'zip': { icon: '🗜️', color: '#6f42c1' },
                    'rar': { icon: '🗜️', color: '#6f42c1' },
                    'mp4': { icon: '🎬', color: '#e83e8c' },
                    'avi': { icon: '🎬', color: '#e83e8c' },
                    'mp3': { icon: '🎵', color: '#20c997' },
                    'jpg': { icon: '🖼️', color: '#fd7e14' },
                    'png': { icon: '🖼️', color: '#fd7e14' },
                    'txt': { icon: '📄', color: '#6c757d' }
                };
                
                return icons[ext] || { icon: '📄', color: '#6c757d' };
            }
            
            renderPagination(totalItems) {
                const totalPages = Math.ceil(totalItems / this.pageSize);
                const pagination = document.getElementById('pagination');
                
                if (totalPages <= 1) {
                    pagination.innerHTML = '';
                    return;
                }
                
                let html = '';
                
                // 上一页
                html += '<button class="page-btn" ' + (this.currentPage === 1 ? 'disabled' : '') + ' onclick="app.goToPage(' + (this.currentPage - 1) + ')">\u4e0a\u4e00\u9875</button>';

                // 页码
                for (let i = Math.max(1, this.currentPage - 2); i <= Math.min(totalPages, this.currentPage + 2); i++) {
                    html += '<button class="page-btn ' + (i === this.currentPage ? 'active' : '') + '" onclick="app.goToPage(' + i + ')">' + i + '</button>';
                }

                // 下一页
                html += '<button class="page-btn" ' + (this.currentPage === totalPages ? 'disabled' : '') + ' onclick="app.goToPage(' + (this.currentPage + 1) + ')">\u4e0b\u4e00\u9875</button>';
                
                pagination.innerHTML = html;
            }
            
            goToPage(page) {
                this.currentPage = page;
                this.renderResults();
            }
            
            showLoading() {
                this.hideAll();
                document.getElementById('loading').style.display = 'block';
            }
            
            showError(message) {
                this.hideAll();
                const errorEl = document.getElementById('error');
                errorEl.textContent = message;
                errorEl.style.display = 'block';
            }
            
            hideAll() {
                document.getElementById('resultsSection').style.display = 'none';
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'none';
                document.getElementById('empty').style.display = 'none';
            }
        }
        
        // 初始化应用
        const app = new PanSearchApp();
    </script>
</body>
</html>`;
