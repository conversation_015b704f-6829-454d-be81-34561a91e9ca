// 基础测试 - 不使用任何导入
describe('基础测试框架验证', () => {
    test('基础数学运算', () => {
        expect(1 + 1).toBe(2);
        expect(2 * 3).toBe(6);
        expect(10 / 2).toBe(5);
    });

    test('字符串操作', () => {
        expect('hello').toBe('hello');
        expect('hello' + ' world').toBe('hello world');
        expect('test'.length).toBe(4);
    });

    test('数组操作', () => {
        const arr = [1, 2, 3];
        expect(arr.length).toBe(3);
        expect(arr[0]).toBe(1);
        expect(arr.includes(2)).toBe(true);
    });

    test('对象操作', () => {
        const obj = { name: 'test', value: 42 };
        expect(obj.name).toBe('test');
        expect(obj.value).toBe(42);
        expect(Object.keys(obj)).toEqual(['name', 'value']);
    });

    test('异步操作', async () => {
        const promise = Promise.resolve('success');
        const result = await promise;
        expect(result).toBe('success');
    });
});
