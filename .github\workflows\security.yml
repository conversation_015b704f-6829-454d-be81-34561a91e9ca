name: Security Scan

on:
  schedule:
    # 每天凌晨2点运行安全扫描
    - cron: '0 2 * * *'
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch: # 允许手动触发

env:
  NODE_VERSION: '18'

jobs:
  # 依赖安全扫描
  dependency-scan:
    name: Dependency Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run npm audit
      run: |
        echo "Running npm audit..."
        npm audit --audit-level=moderate --json > npm-audit-results.json || true
        
    - name: Upload npm audit results
      uses: actions/upload-artifact@v3
      with:
        name: npm-audit-results
        path: npm-audit-results.json
        
    - name: Run Snyk test
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --json > snyk-results.json
      continue-on-error: true
      
    - name: Upload Snyk results
      uses: actions/upload-artifact@v3
      with:
        name: snyk-results
        path: snyk-results.json
      continue-on-error: true
      
    - name: Monitor with Snyk
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        command: monitor
      continue-on-error: true

  # 代码安全扫描
  code-scan:
    name: Code Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: javascript
        
    - name: Autobuild
      uses: github/codeql-action/autobuild@v2
      
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2

  # 密钥扫描
  secret-scan:
    name: Secret Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run TruffleHog
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified

  # 容器安全扫描（如果使用Docker）
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    if: false # 暂时禁用，因为当前项目不使用容器
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Build Docker image
      run: docker build -t pansou-search:latest .
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'pansou-search:latest'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # 安全配置检查
  security-config:
    name: Security Configuration Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Check security headers configuration
      run: |
        echo "Checking security headers configuration..."
        node -e "
        const { getSecurityHeaders, generateCSPHeader } = require('./src/config/security.js');
        const headers = getSecurityHeaders('https://example.com', 'production');
        const csp = generateCSPHeader('production');
        
        console.log('Security Headers:', Object.keys(headers));
        console.log('CSP Policy:', csp);
        
        // 检查必需的安全头部
        const requiredHeaders = [
          'Content-Security-Policy',
          'X-Content-Type-Options',
          'X-Frame-Options',
          'X-XSS-Protection',
          'Strict-Transport-Security'
        ];
        
        const missing = requiredHeaders.filter(header => !headers[header]);
        if (missing.length > 0) {
          console.error('Missing security headers:', missing);
          process.exit(1);
        }
        
        console.log('✓ All required security headers are configured');
        "
        
    - name: Validate CORS configuration
      run: |
        echo "Validating CORS configuration..."
        node -e "
        const { isOriginAllowed, getCORSHeaders } = require('./src/config/security.js');
        
        // 测试允许的源
        const allowedOrigins = [
          'https://pansou.104078.xyz',
          'https://pansou-search.workers.dev'
        ];
        
        // 测试不允许的源
        const disallowedOrigins = [
          'https://malicious.com',
          'http://evil.site'
        ];
        
        allowedOrigins.forEach(origin => {
          if (!isOriginAllowed(origin, 'production')) {
            console.error('Should allow origin:', origin);
            process.exit(1);
          }
        });
        
        disallowedOrigins.forEach(origin => {
          if (isOriginAllowed(origin, 'production')) {
            console.error('Should not allow origin:', origin);
            process.exit(1);
          }
        });
        
        console.log('✓ CORS configuration is valid');
        "

  # 生成安全报告
  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-scan, secret-scan, security-config]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download all artifacts
      uses: actions/download-artifact@v3
      
    - name: Generate security report
      run: |
        echo "# Security Scan Report" > security-report.md
        echo "Generated on: $(date)" >> security-report.md
        echo "" >> security-report.md
        
        echo "## Dependency Scan Results" >> security-report.md
        if [ -f "npm-audit-results/npm-audit-results.json" ]; then
          echo "npm audit results available" >> security-report.md
        else
          echo "npm audit results not available" >> security-report.md
        fi
        
        if [ -f "snyk-results/snyk-results.json" ]; then
          echo "Snyk scan results available" >> security-report.md
        else
          echo "Snyk scan results not available" >> security-report.md
        fi
        
        echo "" >> security-report.md
        echo "## Code Scan Results" >> security-report.md
        echo "CodeQL analysis completed" >> security-report.md
        
        echo "" >> security-report.md
        echo "## Secret Scan Results" >> security-report.md
        echo "TruffleHog scan completed" >> security-report.md
        
        echo "" >> security-report.md
        echo "## Security Configuration" >> security-report.md
        echo "Security headers and CORS configuration validated" >> security-report.md
        
    - name: Upload security report
      uses: actions/upload-artifact@v3
      with:
        name: security-report
        path: security-report.md
        
    - name: Comment PR with security report
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('security-report.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 🔒 Security Scan Report\n\n${report}`
          });
