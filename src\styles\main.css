/* 基础样式重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 3rem;
    font-weight: 300;
    margin-bottom: 16px;
    opacity: 0.95;
}

.header p {
    font-size: 1.1rem;
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* 搜索区域样式 */
.search-section {
    margin-bottom: 40px;
}

.search-box {
    position: relative;
    max-width: 800px;
    margin: 0 auto 30px;
}

.search-input {
    width: 100%;
    padding: 16px 60px 16px 20px;
    font-size: 1rem;
    border: none;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    color: white;
    outline: none;
    transition: all 0.3s ease;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

/* 状态样式 */
.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.1rem;
}

.error {
    text-align: center;
    padding: 40px;
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    border-radius: 12px;
    margin: 20px 0;
}

.empty {
    text-align: center;
    padding: 60px 20px;
    opacity: 0.7;
}

.empty h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}
