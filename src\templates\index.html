<!doctype html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>网盘搜索 - File Storage</title>
        <!-- CSS will be injected here -->
        <style>
            /* Placeholder for CSS content */
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>File Storage</h1>
                <p>发现、整理和管理您的文件，享受直观而强大的搜索体验</p>
            </div>

            <div class="search-section">
                <div class="search-box">
                    <input
                        type="text"
                        class="search-input"
                        placeholder="搜索文件、文件夹和内容..."
                        id="searchInput"
                    />
                    <button class="search-btn" id="searchBtn">🔍</button>
                </div>

                <div class="filters" id="dynamicFilters">
                    <button class="filter-btn active" data-type="all">所有文件</button>
                </div>
            </div>

            <div class="results-section" id="resultsSection" style="display: none">
                <div class="results-header">
                    <div class="results-count" id="resultsCount">找到 0 个文件</div>
                    <div class="results-controls">
                        <div class="sort-controls">
                            <select class="sort-select" id="sortSelect">
                                <option value="default">默认排序</option>
                                <option value="newest">最新优先</option>
                                <option value="oldest">最旧优先</option>
                            </select>
                        </div>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="grid">⊞</button>
                            <button class="view-btn" data-view="list">☰</button>
                        </div>
                    </div>
                </div>

                <div class="results-grid" id="resultsGrid"></div>

                <div class="pagination" id="pagination"></div>
            </div>

            <div class="loading" id="loading" style="display: none">
                <div>🔍 正在搜索...</div>
            </div>

            <div class="error" id="error" style="display: none"></div>

            <div class="empty" id="empty">
                <h3>开始搜索</h3>
                <p>输入关键词来搜索网盘资源</p>
            </div>
        </div>

        <!-- JavaScript will be injected here -->
        <script>
            /* Placeholder for JavaScript content */
        </script>
    </body>
</html>
