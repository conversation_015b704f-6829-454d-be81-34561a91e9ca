# 性能测试配置指南

## 概述

本项目集成了 Lighthouse CI 进行自动化性能测试，确保应用在各个环境下都能保持良好的性能表现。

## 工具和配置

### 1. Lighthouse CI

**配置文件**: `lighthouserc.js`

**主要功能**:
- 自动化性能测试
- 核心 Web 指标监控
- 性能预算检查
- 可访问性评估
- SEO 最佳实践检查

### 2. 性能预算

**配置文件**: `performance-budget.json`

**预算设置**:
- **首屏内容绘制 (FCP)**: < 2秒
- **最大内容绘制 (LCP)**: < 2.5秒
- **累积布局偏移 (CLS)**: < 0.1
- **总阻塞时间 (TBT)**: < 300ms
- **速度指数 (SI)**: < 3秒

### 3. 资源预算

**脚本文件**: < 200KB
**样式文件**: < 50KB
**图片文件**: < 500KB
**字体文件**: < 100KB
**总资源**: < 1MB

## 使用方法

### 本地测试

```bash
# 运行完整的性能测试
npm run test:performance:local

# 仅运行 Lighthouse CI
npm run test:performance

# 启动开发服务器并手动测试
npm run dev
# 在另一个终端运行
npx lighthouse http://localhost:8787 --output=html --output-path=./lighthouse-report.html
```

### CI/CD 集成

性能测试已集成到 GitHub Actions 工作流中：

```yaml
# .github/workflows/ci.yml
performance:
  name: Performance Test
  runs-on: ubuntu-latest
  needs: [build]
  
  steps:
  - name: Run Lighthouse CI
    run: npm run test:performance
```

## 性能指标说明

### 核心 Web 指标

1. **首屏内容绘制 (First Contentful Paint, FCP)**
   - 测量页面开始加载到页面内容的任何部分在屏幕上完成渲染的时间
   - 目标: < 2秒

2. **最大内容绘制 (Largest Contentful Paint, LCP)**
   - 测量页面开始加载到最大文本块或图像元素在屏幕上完成渲染的时间
   - 目标: < 2.5秒

3. **累积布局偏移 (Cumulative Layout Shift, CLS)**
   - 测量页面整个生命周期内发生的所有意外布局偏移的累积分数
   - 目标: < 0.1

4. **总阻塞时间 (Total Blocking Time, TBT)**
   - 测量FCP和TTI之间的总时间，在此期间主线程被阻塞足够长的时间以防止输入响应
   - 目标: < 300ms

### Lighthouse 评分类别

1. **性能 (Performance)**: 80+
2. **可访问性 (Accessibility)**: 90+
3. **最佳实践 (Best Practices)**: 80+
4. **SEO**: 80+

## 性能优化建议

### 1. 资源优化

- **压缩资源**: 使用 gzip/brotli 压缩
- **图片优化**: 使用现代图片格式 (WebP, AVIF)
- **字体优化**: 使用 font-display: swap
- **代码分割**: 按需加载非关键代码

### 2. 加载优化

- **关键资源优先级**: 优先加载关键 CSS 和 JS
- **预加载**: 使用 `<link rel="preload">` 预加载关键资源
- **懒加载**: 对非关键图片和内容使用懒加载
- **缓存策略**: 设置合适的缓存头

### 3. 渲染优化

- **避免布局偏移**: 为图片和广告设置尺寸
- **优化 CSS**: 避免复杂的选择器和大量的重绘
- **减少 DOM 大小**: 保持 DOM 树简洁
- **使用 CSS containment**: 隔离渲染区域

## 监控和报告

### 自动化监控

1. **CI/CD 集成**: 每次部署前自动运行性能测试
2. **定期监控**: 定时运行性能测试检查回归
3. **阈值告警**: 性能指标低于阈值时发送告警

### 报告格式

性能测试会生成以下报告：

1. **JSON 报告**: 包含详细的指标数据
2. **HTML 报告**: 可视化的性能报告
3. **CI 摘要**: 在 PR 中显示性能变化

### 性能趋势分析

```bash
# 查看性能历史趋势
ls -la .lighthouseci/
cat performance-report.json | jq '.details.lighthouse.scores'
```

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查端口是否被占用
   - 确认开发服务器配置正确

2. **Lighthouse 测试超时**
   - 增加超时时间配置
   - 检查网络连接

3. **性能评分低**
   - 分析具体的失败审计项
   - 参考 Lighthouse 建议进行优化

### 调试技巧

1. **本地调试**
   ```bash
   # 生成详细报告
   npx lighthouse http://localhost:8787 --view
   
   # 模拟移动设备
   npx lighthouse http://localhost:8787 --preset=mobile
   
   # 自定义配置
   npx lighthouse http://localhost:8787 --config-path=./lighthouse-config.js
   ```

2. **CI 调试**
   - 查看 GitHub Actions 日志
   - 下载生成的报告文件
   - 检查环境变量配置

## 最佳实践

### 测试策略

1. **多环境测试**: 在不同环境下运行测试
2. **多设备测试**: 测试桌面和移动设备性能
3. **网络条件**: 模拟不同的网络条件
4. **用户场景**: 测试关键用户路径

### 性能预算管理

1. **设置合理预算**: 基于用户期望和业务需求
2. **渐进式改进**: 逐步提高性能标准
3. **团队共识**: 确保团队理解性能重要性
4. **持续监控**: 定期审查和调整预算

### 优化工作流

1. **性能优先**: 在开发过程中考虑性能影响
2. **早期测试**: 在开发阶段就进行性能测试
3. **自动化**: 将性能测试集成到开发流程
4. **数据驱动**: 基于实际数据进行优化决策
