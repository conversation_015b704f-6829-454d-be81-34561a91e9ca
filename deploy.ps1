# Cloudflare Workers 部署脚本
# PowerShell 脚本用于 Windows 系统

Write-Host "开始部署网盘搜索应用到 Cloudflare Workers..." -ForegroundColor Green

# 检查是否安装了 Node.js
try {
    $nodeVersion = node --version
    Write-Host "Node.js 版本: $nodeVersion" -ForegroundColor Blue
} catch {
    Write-Host "错误: 未找到 Node.js，请先安装 Node.js" -ForegroundColor Red
    exit 1
}

# 检查是否安装了依赖
if (!(Test-Path "node_modules")) {
    Write-Host "安装依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 依赖安装失败" -ForegroundColor Red
        exit 1
    }
}

# 检查 Wrangler 登录状态
Write-Host "检查 Wrangler 登录状态..." -ForegroundColor Yellow
npx wrangler whoami
if ($LASTEXITCODE -ne 0) {
    Write-Host "请先登录 Cloudflare:" -ForegroundColor Yellow
    npx wrangler login
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: Cloudflare 登录失败" -ForegroundColor Red
        exit 1
    }
}

# 询问部署环境
$env = Read-Host "选择部署环境 (1: 开发测试, 2: 生产环境) [默认: 1]"
if ([string]::IsNullOrEmpty($env)) {
    $env = "1"
}

switch ($env) {
    "1" {
        Write-Host "部署到开发环境..." -ForegroundColor Yellow
        npx wrangler deploy --env staging
    }
    "2" {
        Write-Host "部署到生产环境..." -ForegroundColor Yellow
        npx wrangler deploy --env production
    }
    default {
        Write-Host "部署到默认环境..." -ForegroundColor Yellow
        npx wrangler deploy
    }
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "部署成功! 🎉" -ForegroundColor Green
    Write-Host "您的网盘搜索应用已经部署完成。" -ForegroundColor Green
} else {
    Write-Host "部署失败，请检查错误信息。" -ForegroundColor Red
    exit 1
}
