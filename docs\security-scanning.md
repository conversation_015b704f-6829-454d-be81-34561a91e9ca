# 安全扫描配置指南

## 概述

本项目集成了多种安全扫描工具，用于检测依赖漏洞和安全问题。

## 工具列表

### 1. npm audit
- **用途**: 检测npm依赖包中的已知安全漏洞
- **命令**: `npm run security:audit`
- **修复**: `npm run security:audit:fix`

### 2. Snyk
- **用途**: 深度安全漏洞扫描和监控
- **命令**: `npm run security:snyk`
- **监控**: `npm run security:snyk:monitor`
- **配置文件**: `.snyk`

### 3. Lighthouse CI
- **用途**: 性能和安全最佳实践检查
- **命令**: `npm run test:performance`
- **配置文件**: `lighthouserc.js`

## 使用方法

### 本地开发
```bash
# 运行所有安全检查
npm run security:all

# 单独运行npm audit
npm run security:audit

# 单独运行Snyk扫描
npm run security:snyk
```

### CI/CD集成
安全扫描已集成到GitHub Actions工作流中，会在每次提交时自动运行。

## 配置说明

### Snyk配置 (.snyk)
- 可以配置忽略特定漏洞
- 支持设置过期时间
- 可以区分开发依赖和生产依赖

### Lighthouse配置 (lighthouserc.js)
- 设置性能预算
- 配置安全检查项
- 定义核心Web指标阈值

## 安全策略

1. **依赖管理**
   - 定期更新依赖包
   - 及时修复安全漏洞
   - 使用锁定文件确保版本一致性

2. **漏洞处理**
   - 高危漏洞立即修复
   - 中危漏洞在下个版本修复
   - 低危漏洞定期评估

3. **监控报告**
   - 每日自动扫描
   - 漏洞发现时邮件通知
   - 定期生成安全报告
