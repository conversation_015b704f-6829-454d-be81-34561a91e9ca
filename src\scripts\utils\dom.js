/**
 * DOM 操作工具函数
 * 提供常用的 DOM 操作和事件处理功能
 */

/**
 * 根据ID获取元素
 * @param {string} id - 元素ID
 * @returns {HTMLElement|null} DOM元素
 */
export function getElementById(id) {
    return document.getElementById(id);
}

/**
 * 根据选择器获取元素
 * @param {string} selector - CSS选择器
 * @param {HTMLElement} parent - 父元素，默认为document
 * @returns {HTMLElement|null} DOM元素
 */
export function querySelector(selector, parent = document) {
    return parent.querySelector(selector);
}

/**
 * 根据选择器获取所有匹配元素
 * @param {string} selector - CSS选择器
 * @param {HTMLElement} parent - 父元素，默认为document
 * @returns {NodeList} DOM元素列表
 */
export function querySelectorAll(selector, parent = document) {
    return parent.querySelectorAll(selector);
}

/**
 * 创建DOM元素
 * @param {string} tagName - 标签名
 * @param {Object} attributes - 属性对象
 * @param {string} textContent - 文本内容
 * @returns {HTMLElement} 创建的DOM元素
 */
export function createElement(tagName, attributes = {}, textContent = '') {
    const element = document.createElement(tagName);

    Object.entries(attributes).forEach(([key, value]) => {
        if (key === 'className') {
            element.className = value;
        } else if (key === 'dataset') {
            Object.entries(value).forEach(([dataKey, dataValue]) => {
                element.dataset[dataKey] = dataValue;
            });
        } else {
            element.setAttribute(key, value);
        }
    });

    if (textContent) {
        element.textContent = textContent;
    }

    return element;
}

/**
 * 添加CSS类
 * @param {HTMLElement} element - DOM元素
 * @param {string|Array} className - 类名或类名数组
 */
export function addClass(element, className) {
    if (!element) {
        return;
    }

    if (Array.isArray(className)) {
        element.classList.add(...className);
    } else {
        element.classList.add(className);
    }
}

/**
 * 移除CSS类
 * @param {HTMLElement} element - DOM元素
 * @param {string|Array} className - 类名或类名数组
 */
export function removeClass(element, className) {
    if (!element) {
        return;
    }

    if (Array.isArray(className)) {
        element.classList.remove(...className);
    } else {
        element.classList.remove(className);
    }
}

/**
 * 切换CSS类
 * @param {HTMLElement} element - DOM元素
 * @param {string} className - 类名
 * @returns {boolean} 切换后是否包含该类
 */
export function toggleClass(element, className) {
    if (!element) {
        return false;
    }
    return element.classList.toggle(className);
}

/**
 * 检查元素是否包含指定类
 * @param {HTMLElement} element - DOM元素
 * @param {string} className - 类名
 * @returns {boolean} 是否包含该类
 */
export function hasClass(element, className) {
    if (!element) {
        return false;
    }
    return element.classList.contains(className);
}

/**
 * 设置元素样式
 * @param {HTMLElement} element - DOM元素
 * @param {Object} styles - 样式对象
 */
export function setStyles(element, styles) {
    if (!element) {
        return;
    }

    Object.entries(styles).forEach(([property, value]) => {
        element.style[property] = value;
    });
}

/**
 * 显示元素
 * @param {HTMLElement} element - DOM元素
 * @param {string} display - 显示类型，默认为'block'
 */
export function show(element, display = 'block') {
    if (element) {
        element.style.display = display;
    }
}

/**
 * 隐藏元素
 * @param {HTMLElement} element - DOM元素
 */
export function hide(element) {
    if (element) {
        element.style.display = 'none';
    }
}

/**
 * 切换元素显示/隐藏
 * @param {HTMLElement} element - DOM元素
 * @param {string} display - 显示类型，默认为'block'
 */
export function toggle(element, display = 'block') {
    if (!element) {
        return;
    }

    if (element.style.display === 'none') {
        show(element, display);
    } else {
        hide(element);
    }
}

/**
 * 添加事件监听器
 * @param {HTMLElement} element - DOM元素
 * @param {string} event - 事件类型
 * @param {Function} handler - 事件处理函数
 * @param {Object} options - 事件选项
 */
export function addEventListener(element, event, handler, options = {}) {
    if (element) {
        element.addEventListener(event, handler, options);
    }
}

/**
 * 移除事件监听器
 * @param {HTMLElement} element - DOM元素
 * @param {string} event - 事件类型
 * @param {Function} handler - 事件处理函数
 */
export function removeEventListener(element, event, handler) {
    if (element) {
        element.removeEventListener(event, handler);
    }
}

/**
 * 委托事件处理
 * @param {HTMLElement} parent - 父元素
 * @param {string} selector - 子元素选择器
 * @param {string} event - 事件类型
 * @param {Function} handler - 事件处理函数
 */
export function delegate(parent, selector, event, handler) {
    if (!parent) {
        return;
    }

    parent.addEventListener(event, e => {
        const target = e.target.closest(selector);
        if (target) {
            handler.call(target, e);
        }
    });
}

/**
 * 清空元素内容
 * @param {HTMLElement} element - DOM元素
 */
export function empty(element) {
    if (element) {
        element.innerHTML = '';
    }
}

/**
 * 设置元素HTML内容
 * @param {HTMLElement} element - DOM元素
 * @param {string} html - HTML内容
 */
export function setHTML(element, html) {
    if (element) {
        element.innerHTML = html;
    }
}

/**
 * 设置元素文本内容
 * @param {HTMLElement} element - DOM元素
 * @param {string} text - 文本内容
 */
export function setText(element, text) {
    if (element) {
        element.textContent = text;
    }
}

/**
 * 获取元素文本内容
 * @param {HTMLElement} element - DOM元素
 * @returns {string} 文本内容
 */
export function getText(element) {
    return element ? element.textContent : '';
}

/**
 * 等待DOM加载完成
 * @param {Function} callback - 回调函数
 */
export function ready(callback) {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', callback);
    } else {
        callback();
    }
}
