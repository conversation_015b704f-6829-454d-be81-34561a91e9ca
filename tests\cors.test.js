import { 
    isOriginAllowed, 
    getCORSHeaders, 
    getSecurityHeaders,
    generateCSPHeader 
} from '../src/config/security.js';

/**
 * CORS配置测试
 */
function runCORSTests() {
    console.log('开始运行CORS配置测试...');
    
    // 测试允许的源检查
    testOriginAllowed();
    
    // 测试CORS头部生成
    testCORSHeaders();
    
    // 测试安全头部生成
    testSecurityHeaders();
    
    // 测试CSP头部生成
    testCSPHeader();
    
    console.log('所有CORS配置测试完成！');
}

function testOriginAllowed() {
    console.log('测试源域名检查...');
    
    // 测试允许的生产环境域名
    const allowedOrigins = [
        'https://pansou.104078.xyz',
        'https://pansou-search.workers.dev',
        'https://pansou-search-staging.workers.dev'
    ];
    
    allowedOrigins.forEach(origin => {
        const result = isOriginAllowed(origin, 'production');
        if (result) {
            console.log(`✓ 生产环境允许源: ${origin}`);
        } else {
            console.error(`✗ 生产环境应该允许源: ${origin}`);
        }
    });
    
    // 测试开发环境localhost
    const devOrigins = [
        'http://localhost:8787',
        'http://127.0.0.1:8787'
    ];
    
    devOrigins.forEach(origin => {
        const result = isOriginAllowed(origin, 'development');
        if (result) {
            console.log(`✓ 开发环境允许源: ${origin}`);
        } else {
            console.error(`✗ 开发环境应该允许源: ${origin}`);
        }
    });
    
    // 测试不允许的源
    const disallowedOrigins = [
        'https://malicious.com',
        'http://evil.site',
        'https://phishing.example'
    ];
    
    disallowedOrigins.forEach(origin => {
        const result = isOriginAllowed(origin, 'production');
        if (!result) {
            console.log(`✓ 正确拒绝恶意源: ${origin}`);
        } else {
            console.error(`✗ 应该拒绝恶意源: ${origin}`);
        }
    });
    
    // 测试空源
    const emptyResult = isOriginAllowed(null, 'production');
    if (!emptyResult) {
        console.log('✓ 正确拒绝空源');
    } else {
        console.error('✗ 应该拒绝空源');
    }
}

function testCORSHeaders() {
    console.log('测试CORS头部生成...');
    
    // 测试允许的源
    const allowedOrigin = 'https://pansou.104078.xyz';
    const headers = getCORSHeaders(allowedOrigin, 'production');
    
    if (headers['Access-Control-Allow-Origin'] === allowedOrigin) {
        console.log('✓ 正确设置允许的源');
    } else {
        console.error('✗ 未正确设置允许的源');
    }
    
    if (headers['Access-Control-Allow-Methods']) {
        console.log('✓ 设置了允许的方法');
    } else {
        console.error('✗ 未设置允许的方法');
    }
    
    if (headers['Access-Control-Allow-Headers']) {
        console.log('✓ 设置了允许的头部');
    } else {
        console.error('✗ 未设置允许的头部');
    }
    
    // 测试不允许的源
    const disallowedOrigin = 'https://malicious.com';
    const emptyHeaders = getCORSHeaders(disallowedOrigin, 'production');
    
    if (Object.keys(emptyHeaders).length === 0) {
        console.log('✓ 正确拒绝不允许的源，返回空头部');
    } else {
        console.error('✗ 应该拒绝不允许的源');
    }
}

function testSecurityHeaders() {
    console.log('测试安全头部生成...');
    
    const origin = 'https://pansou.104078.xyz';
    const headers = getSecurityHeaders(origin, 'production');
    
    // 检查关键安全头部
    const requiredHeaders = [
        'X-Content-Type-Options',
        'X-XSS-Protection',
        'X-Frame-Options',
        'Referrer-Policy',
        'Content-Security-Policy'
    ];
    
    requiredHeaders.forEach(header => {
        if (headers[header]) {
            console.log(`✓ 设置了安全头部: ${header}`);
        } else {
            console.error(`✗ 未设置安全头部: ${header}`);
        }
    });
    
    // 检查HSTS头部（仅生产环境）
    if (headers['Strict-Transport-Security']) {
        console.log('✓ 生产环境设置了HSTS头部');
    } else {
        console.error('✗ 生产环境应该设置HSTS头部');
    }
    
    // 测试开发环境（不应该有HSTS）
    const devHeaders = getSecurityHeaders(origin, 'development');
    if (!devHeaders['Strict-Transport-Security']) {
        console.log('✓ 开发环境正确移除了HSTS头部');
    } else {
        console.error('✗ 开发环境不应该设置HSTS头部');
    }
}

function testCSPHeader() {
    console.log('测试CSP头部生成...');
    
    const cspHeader = generateCSPHeader();
    
    if (cspHeader && cspHeader.length > 0) {
        console.log('✓ 成功生成CSP头部');
        console.log(`CSP头部内容: ${cspHeader.substring(0, 100)}...`);
    } else {
        console.error('✗ 未能生成CSP头部');
    }
    
    // 检查关键CSP指令
    const requiredDirectives = [
        'default-src',
        'script-src',
        'style-src',
        'img-src',
        'connect-src'
    ];
    
    requiredDirectives.forEach(directive => {
        if (cspHeader.includes(directive)) {
            console.log(`✓ CSP包含指令: ${directive}`);
        } else {
            console.error(`✗ CSP缺少指令: ${directive}`);
        }
    });
    
    // 检查是否包含不安全的配置
    if (cspHeader.includes("'unsafe-eval'")) {
        console.error('✗ CSP包含不安全的eval配置');
    } else {
        console.log('✓ CSP不包含不安全的eval配置');
    }
}

// 运行测试
runCORSTests();
