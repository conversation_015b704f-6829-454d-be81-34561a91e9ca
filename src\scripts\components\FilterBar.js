/**
 * 筛选栏组件
 * 负责动态生成筛选按钮和处理筛选事件
 */
export class FilterBar {
    constructor(onFilterChange) {
        this.onFilterChange = onFilterChange;
        this.currentFilter = 'all';
        this.availablePlatforms = new Map();
        this.init();
    }

    init() {
        this.container = document.getElementById('dynamicFilters');
    }

    /**
     * 生成动态筛选按钮
     * @param {Map} platforms - 平台信息映射
     * @param {number} totalCount - 总结果数量
     */
    generateFilters(platforms, totalCount) {
        this.availablePlatforms = platforms;

        if (!this.container) {
            return;
        }

        // 清除现有的动态筛选按钮，保留"所有文件"按钮
        const allFilesBtn = this.container.querySelector('[data-type="all"]');
        this.container.innerHTML = '';

        if (allFilesBtn) {
            this.container.appendChild(allFilesBtn);
        } else {
            // 如果没有"所有文件"按钮，创建一个
            this.createAllFilesButton();
        }

        // 更新"所有文件"按钮的计数
        const allBtn = this.container.querySelector('[data-type="all"]');
        if (allBtn) {
            allBtn.innerHTML = `所有文件 <span class="count">(${totalCount})</span>`;
        }

        // 生成动态筛选按钮
        platforms.forEach((info, platform) => {
            this.createFilterButton(platform, info);
        });

        // 重新激活当前筛选
        this.setActiveFilter(this.currentFilter);
    }

    createAllFilesButton() {
        const btn = document.createElement('button');
        btn.className = 'filter-btn active';
        btn.dataset.type = 'all';
        btn.innerHTML = '所有文件';

        btn.addEventListener('click', e => {
            this.handleFilterClick(e);
        });

        this.container.appendChild(btn);
    }

    createFilterButton(platform, info) {
        const btn = document.createElement('button');
        btn.className = 'filter-btn';
        btn.dataset.type = platform;
        btn.innerHTML = `${info.name} <span class="count">(${info.count})</span>`;

        btn.addEventListener('click', e => {
            this.handleFilterClick(e);
        });

        this.container.appendChild(btn);
    }

    handleFilterClick(e) {
        const filterType = e.target.dataset.type;
        if (filterType) {
            this.setActiveFilter(filterType);
            this.currentFilter = filterType;

            if (this.onFilterChange) {
                this.onFilterChange(filterType);
            }
        }
    }

    setActiveFilter(filterType) {
        // 移除所有按钮的 active 类
        const buttons = this.container.querySelectorAll('.filter-btn');
        buttons.forEach(btn => btn.classList.remove('active'));

        // 激活指定的按钮
        const activeBtn = this.container.querySelector(`[data-type="${filterType}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
    }

    getCurrentFilter() {
        return this.currentFilter;
    }

    setCurrentFilter(filterType) {
        this.currentFilter = filterType;
        this.setActiveFilter(filterType);
    }

    setLoading(isLoading) {
        if (this.container) {
            if (isLoading) {
                this.container.classList.add('loading');
            } else {
                this.container.classList.remove('loading');
            }
        }
    }

    reset() {
        this.currentFilter = 'all';
        this.availablePlatforms.clear();

        if (this.container) {
            this.container.innerHTML = '';
            this.createAllFilesButton();
        }
    }
}
