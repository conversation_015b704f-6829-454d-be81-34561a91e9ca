/**
 * 分页组件
 * 负责处理分页显示和分页事件
 */
export class Pagination {
    constructor(onPageChange) {
        this.onPageChange = onPageChange;
        this.currentPage = 1;
        this.pageSize = 12;
        this.totalItems = 0;
        this.container = document.getElementById('pagination');
    }

    /**
     * 渲染分页组件
     * @param {number} totalItems - 总项目数
     * @param {number} currentPage - 当前页码
     * @param {number} pageSize - 每页大小
     */
    render(totalItems, currentPage = 1, pageSize = 12) {
        this.totalItems = totalItems;
        this.currentPage = currentPage;
        this.pageSize = pageSize;

        if (!this.container) {
            return;
        }

        const totalPages = Math.ceil(totalItems / pageSize);

        if (totalPages <= 1) {
            this.container.innerHTML = '';
            return;
        }

        let html = '';

        // 上一页按钮
        html += this.createPageButton('上一页', currentPage - 1, currentPage === 1);

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        // 如果开始页码大于1，显示第一页和省略号
        if (startPage > 1) {
            html += this.createPageButton('1', 1, false, currentPage === 1);
            if (startPage > 2) {
                html += '<span class="page-ellipsis">...</span>';
            }
        }

        // 显示页码范围
        for (let i = startPage; i <= endPage; i++) {
            html += this.createPageButton(i.toString(), i, false, i === currentPage);
        }

        // 如果结束页码小于总页数，显示省略号和最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += '<span class="page-ellipsis">...</span>';
            }
            html += this.createPageButton(
                totalPages.toString(),
                totalPages,
                false,
                currentPage === totalPages
            );
        }

        // 下一页按钮
        html += this.createPageButton('下一页', currentPage + 1, currentPage === totalPages);

        this.container.innerHTML = html;
        this.bindEvents();
    }

    /**
     * 创建分页按钮HTML
     * @param {string} text - 按钮文本
     * @param {number} page - 页码
     * @param {boolean} disabled - 是否禁用
     * @param {boolean} active - 是否激活
     * @returns {string} HTML字符串
     */
    createPageButton(text, page, disabled = false, active = false) {
        const classes = ['page-btn'];
        if (disabled) {
            classes.push('disabled');
        }
        if (active) {
            classes.push('active');
        }

        const disabledAttr = disabled ? 'disabled' : '';
        const dataPage = disabled ? '' : `data-page="${page}"`;

        return `<button class="${classes.join(' ')}" ${disabledAttr} ${dataPage}>${text}</button>`;
    }

    /**
     * 绑定分页按钮事件
     */
    bindEvents() {
        if (!this.container) {
            return;
        }

        const buttons = this.container.querySelectorAll('.page-btn[data-page]');
        buttons.forEach(button => {
            button.addEventListener('click', e => {
                const page = parseInt(e.target.dataset.page);
                if (page && page !== this.currentPage) {
                    this.goToPage(page);
                }
            });
        });
    }

    /**
     * 跳转到指定页码
     * @param {number} page - 目标页码
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.totalItems / this.pageSize);

        if (page < 1 || page > totalPages || page === this.currentPage) {
            return;
        }

        this.currentPage = page;

        if (this.onPageChange) {
            this.onPageChange(page);
        }
    }

    /**
     * 获取当前页码
     * @returns {number} 当前页码
     */
    getCurrentPage() {
        return this.currentPage;
    }

    /**
     * 设置当前页码
     * @param {number} page - 页码
     */
    setCurrentPage(page) {
        this.currentPage = page;
    }

    /**
     * 获取每页大小
     * @returns {number} 每页大小
     */
    getPageSize() {
        return this.pageSize;
    }

    /**
     * 设置每页大小
     * @param {number} size - 每页大小
     */
    setPageSize(size) {
        this.pageSize = size;
    }

    /**
     * 重置分页到第一页
     */
    reset() {
        this.currentPage = 1;
        this.totalItems = 0;
        if (this.container) {
            this.container.innerHTML = '';
        }
    }

    /**
     * 清空分页显示
     */
    clear() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}
