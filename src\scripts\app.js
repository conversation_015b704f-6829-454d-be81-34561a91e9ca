import { SearchBox } from './components/SearchBox.js';
import { FilterBar } from './components/FilterBar.js';
import { ResultGrid } from './components/ResultGrid.js';
import { Pagination } from './components/Pagination.js';
import { apiRequest } from './utils/api.js';
import { getPlatformName } from './utils/helpers.js';

/**
 * 主应用类
 * 协调各个组件的工作，管理应用状态
 */
export class PanSearchApp {
    constructor() {
        this.currentResults = [];
        this.currentFilter = 'all';
        this.currentSort = 'default';
        this.availablePlatforms = new Map();

        this.init();
    }

    init() {
        this.initComponents();
        this.bindEvents();
    }

    initComponents() {
        // 初始化各个组件
        this.searchBox = new SearchBox(keyword => this.search(keyword));
        this.filterBar = new FilterBar(filter => this.handleFilterChange(filter));
        this.resultGrid = new ResultGrid();
        this.pagination = new Pagination(page => this.handlePageChange(page));

        // 绑定排序变化事件
        this.resultGrid.onSortChange(sortType => this.handleSortChange(sortType));
    }

    bindEvents() {
        // 这里可以添加其他全局事件绑定
    }

    /**
     * 执行搜索
     * @param {string} keyword - 搜索关键词
     */
    async search(keyword) {
        if (!keyword) {
            return;
        }

        this.showLoading();

        try {
            const data = await apiRequest('/api/search', { kw: keyword });

            if (data.code === 0) {
                this.currentResults = this.processResults(data.data);
                this.generateDynamicFilters();
                this.resetFiltersAndSort();
                this.renderResults();
            } else {
                this.showError(`搜索失败：${data.message || '未知错误'}`);
            }
        } catch (error) {
            console.error('Search error:', error);
            this.showError('网络错误，请稍后重试');
        }
    }

    /**
     * 处理搜索结果数据
     * @param {Object} data - API返回的数据
     * @returns {Array} 处理后的结果数组
     */
    processResults(data) {
        const results = [];
        this.availablePlatforms.clear();

        if (data.merged_by_type) {
            Object.entries(data.merged_by_type).forEach(([platform, items]) => {
                // 记录平台和数量
                this.availablePlatforms.set(platform, {
                    name: getPlatformName(platform),
                    count: items.length
                });

                items.forEach(item => {
                    results.push({
                        ...item,
                        platform: platform,
                        platformName: getPlatformName(platform)
                    });
                });
            });
        }

        return results;
    }

    /**
     * 生成动态筛选器
     */
    generateDynamicFilters() {
        const totalCount = this.currentResults.length;
        this.filterBar.generateFilters(this.availablePlatforms, totalCount);
    }

    /**
     * 重置筛选和排序状态
     */
    resetFiltersAndSort() {
        this.currentFilter = 'all';
        this.currentSort = 'default';
        this.filterBar.setCurrentFilter('all');
        this.resultGrid.setSortType('default');
        this.pagination.reset();
    }

    /**
     * 处理筛选变化
     * @param {string} filter - 筛选类型
     */
    handleFilterChange(filter) {
        this.currentFilter = filter;
        this.pagination.setCurrentPage(1);
        this.renderResults();
    }

    /**
     * 处理排序变化
     * @param {string} sortType - 排序类型
     */
    handleSortChange(sortType) {
        this.currentSort = sortType;
        this.pagination.setCurrentPage(1);
        this.renderResults();
    }

    /**
     * 处理页码变化
     * @param {number} page - 页码
     */
    handlePageChange(page) {
        this.pagination.setCurrentPage(page);
        this.renderResults();
    }

    /**
     * 获取筛选后的结果
     * @returns {Array} 筛选后的结果数组
     */
    getFilteredResults() {
        let results;
        if (this.currentFilter === 'all') {
            results = [...this.currentResults];
        } else {
            results = this.currentResults.filter(item => item.platform === this.currentFilter);
        }

        return this.sortResults(results);
    }

    /**
     * 对结果进行排序
     * @param {Array} results - 结果数组
     * @returns {Array} 排序后的结果数组
     */
    sortResults(results) {
        if (this.currentSort === 'default') {
            return results;
        }

        return results.sort((a, b) => {
            const dateA = a.datetime ? new Date(a.datetime) : new Date(0);
            const dateB = b.datetime ? new Date(b.datetime) : new Date(0);

            if (this.currentSort === 'newest') {
                return dateB - dateA; // 最新优先
            } else if (this.currentSort === 'oldest') {
                return dateA - dateB; // 最旧优先
            }

            return 0;
        });
    }

    /**
     * 渲染搜索结果
     */
    renderResults() {
        const filteredResults = this.getFilteredResults();
        const currentPage = this.pagination.getCurrentPage();
        const pageSize = this.pagination.getPageSize();
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const pageResults = filteredResults.slice(startIndex, endIndex);

        this.hideAll();
        this.resultGrid.render(pageResults, filteredResults.length);
        this.pagination.render(filteredResults.length, currentPage, pageSize);
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        this.hideAll();
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'block';
        }
        this.filterBar.setLoading(true);
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误消息
     */
    showError(message) {
        this.hideAll();
        const errorEl = document.getElementById('error');
        if (errorEl) {
            errorEl.textContent = message;
            errorEl.style.display = 'block';
        }
        this.filterBar.setLoading(false);
    }

    /**
     * 隐藏所有状态元素
     */
    hideAll() {
        const elements = ['resultsSection', 'loading', 'error', 'empty'];
        elements.forEach(id => {
            const el = document.getElementById(id);
            if (el) {
                el.style.display = 'none';
            }
        });
        this.filterBar.setLoading(false);
    }
}
