/**
 * 键盘快捷键管理器
 * 提供全局键盘快捷键支持
 */
export class KeyboardShortcuts {
    constructor() {
        this.shortcuts = new Map();
        this.isEnabled = true;
        this.init();
    }

    init() {
        this.bindGlobalEvents();
        this.registerDefaultShortcuts();
    }

    bindGlobalEvents() {
        document.addEventListener('keydown', (e) => {
            if (!this.isEnabled) return;
            
            const key = this.getKeyString(e);
            const shortcut = this.shortcuts.get(key);
            
            if (shortcut && this.shouldTrigger(e, shortcut)) {
                e.preventDefault();
                shortcut.handler(e);
            }
        });
    }

    getKeyString(event) {
        const parts = [];
        
        if (event.ctrlKey) parts.push('Ctrl');
        if (event.altKey) parts.push('Alt');
        if (event.shiftKey) parts.push('Shift');
        if (event.metaKey) parts.push('Meta');
        
        // 处理特殊键
        let key = event.key;
        if (key === ' ') key = 'Space';
        if (key === 'Escape') key = 'Esc';
        
        parts.push(key);
        
        return parts.join('+');
    }

    shouldTrigger(event, shortcut) {
        // 检查是否在输入框中
        const activeElement = document.activeElement;
        const isInInput = activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true'
        );
        
        // 如果快捷键设置为在输入框中也生效，或者当前不在输入框中
        return shortcut.allowInInput || !isInInput;
    }

    register(key, handler, options = {}) {
        const shortcut = {
            key,
            handler,
            description: options.description || '',
            allowInInput: options.allowInInput || false,
            enabled: options.enabled !== false
        };
        
        this.shortcuts.set(key, shortcut);
    }

    unregister(key) {
        this.shortcuts.delete(key);
    }

    enable() {
        this.isEnabled = true;
    }

    disable() {
        this.isEnabled = false;
    }

    getShortcuts() {
        return Array.from(this.shortcuts.entries()).map(([key, shortcut]) => ({
            key,
            ...shortcut
        }));
    }

    registerDefaultShortcuts() {
        // 搜索相关快捷键
        this.register('Ctrl+k', () => {
            this.focusSearchBox();
        }, {
            description: '聚焦搜索框',
            allowInInput: false
        });

        this.register('/', () => {
            this.focusSearchBox();
        }, {
            description: '聚焦搜索框',
            allowInInput: false
        });

        this.register('Esc', () => {
            this.clearSearchOrBlur();
        }, {
            description: '清空搜索或失焦',
            allowInInput: true
        });

        // 导航快捷键
        this.register('j', () => {
            this.navigateResults('down');
        }, {
            description: '下一个结果',
            allowInInput: false
        });

        this.register('k', () => {
            this.navigateResults('up');
        }, {
            description: '上一个结果',
            allowInInput: false
        });

        this.register('Enter', () => {
            this.openSelectedResult();
        }, {
            description: '打开选中的结果',
            allowInInput: false
        });

        // 筛选快捷键
        this.register('f', () => {
            this.toggleFilters();
        }, {
            description: '切换筛选器',
            allowInInput: false
        });

        this.register('c', () => {
            this.clearFilters();
        }, {
            description: '清除所有筛选',
            allowInInput: false
        });

        // 排序快捷键
        this.register('s', () => {
            this.cycleSortOrder();
        }, {
            description: '切换排序方式',
            allowInInput: false
        });

        // 页面导航
        this.register('g+g', () => {
            this.scrollToTop();
        }, {
            description: '回到顶部',
            allowInInput: false
        });

        this.register('G', () => {
            this.scrollToBottom();
        }, {
            description: '滚动到底部',
            allowInInput: false
        });

        // 帮助快捷键
        this.register('?', () => {
            this.showHelp();
        }, {
            description: '显示快捷键帮助',
            allowInInput: false
        });

        // 刷新
        this.register('r', () => {
            this.refreshResults();
        }, {
            description: '刷新搜索结果',
            allowInInput: false
        });
    }

    focusSearchBox() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    clearSearchOrBlur() {
        const activeElement = document.activeElement;
        
        if (activeElement && activeElement.id === 'searchInput') {
            if (activeElement.value) {
                activeElement.value = '';
                // 触发输入事件
                activeElement.dispatchEvent(new Event('input', { bubbles: true }));
            } else {
                activeElement.blur();
            }
        }
    }

    navigateResults(direction) {
        const results = document.querySelectorAll('.result-item');
        if (!results.length) return;

        let currentIndex = -1;
        const selected = document.querySelector('.result-item.keyboard-selected');
        
        if (selected) {
            currentIndex = Array.from(results).indexOf(selected);
            selected.classList.remove('keyboard-selected');
        }

        let newIndex;
        if (direction === 'down') {
            newIndex = currentIndex < results.length - 1 ? currentIndex + 1 : 0;
        } else {
            newIndex = currentIndex > 0 ? currentIndex - 1 : results.length - 1;
        }

        const newSelected = results[newIndex];
        newSelected.classList.add('keyboard-selected');
        newSelected.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    openSelectedResult() {
        const selected = document.querySelector('.result-item.keyboard-selected');
        if (selected) {
            const link = selected.querySelector('.result-link');
            if (link) {
                link.click();
            }
        }
    }

    toggleFilters() {
        const filterBar = document.querySelector('.filter-bar');
        if (filterBar) {
            filterBar.classList.toggle('expanded');
        }
    }

    clearFilters() {
        const filterButtons = document.querySelectorAll('.filter-btn.active');
        filterButtons.forEach(btn => btn.click());
    }

    cycleSortOrder() {
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            const options = sortSelect.options;
            const currentIndex = sortSelect.selectedIndex;
            const nextIndex = (currentIndex + 1) % options.length;
            sortSelect.selectedIndex = nextIndex;
            sortSelect.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }

    scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    scrollToBottom() {
        window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    }

    refreshResults() {
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        if (searchInput && searchInput.value && searchBtn) {
            searchBtn.click();
        }
    }

    showHelp() {
        this.createHelpModal();
    }

    createHelpModal() {
        // 移除已存在的帮助模态框
        const existingModal = document.getElementById('keyboard-help-modal');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.id = 'keyboard-help-modal';
        modal.className = 'keyboard-help-modal';
        
        const shortcuts = this.getShortcuts().filter(s => s.enabled && s.description);
        
        modal.innerHTML = `
            <div class="keyboard-help-content">
                <div class="keyboard-help-header">
                    <h3>键盘快捷键</h3>
                    <button class="keyboard-help-close">&times;</button>
                </div>
                <div class="keyboard-help-body">
                    ${shortcuts.map(shortcut => `
                        <div class="keyboard-help-item">
                            <kbd class="keyboard-help-key">${shortcut.key}</kbd>
                            <span class="keyboard-help-desc">${shortcut.description}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定关闭事件
        const closeBtn = modal.querySelector('.keyboard-help-close');
        closeBtn.addEventListener('click', () => modal.remove());
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // ESC 关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
    }

    static createStyles() {
        const styles = `
            <style id="keyboard-shortcuts-styles">
                .result-item.keyboard-selected {
                    outline: 2px solid #3b82f6;
                    outline-offset: 2px;
                    background-color: #eff6ff;
                }
                
                .keyboard-help-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                }
                
                .keyboard-help-content {
                    background: white;
                    border-radius: 8px;
                    max-width: 500px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
                }
                
                .keyboard-help-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px;
                    border-bottom: 1px solid #e5e7eb;
                }
                
                .keyboard-help-header h3 {
                    margin: 0;
                    font-size: 18px;
                    font-weight: 600;
                }
                
                .keyboard-help-close {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #6b7280;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4px;
                }
                
                .keyboard-help-close:hover {
                    background-color: #f3f4f6;
                }
                
                .keyboard-help-body {
                    padding: 20px;
                }
                
                .keyboard-help-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 12px;
                }
                
                .keyboard-help-key {
                    background: #f3f4f6;
                    border: 1px solid #d1d5db;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-family: monospace;
                    font-size: 12px;
                    min-width: 60px;
                    text-align: center;
                    margin-right: 16px;
                }
                
                .keyboard-help-desc {
                    color: #374151;
                    font-size: 14px;
                }
            </style>
        `;
        
        if (!document.querySelector('#keyboard-shortcuts-styles')) {
            document.head.insertAdjacentHTML('beforeend', styles);
        }
    }
}

// 创建全局键盘快捷键实例
export const globalKeyboardShortcuts = new KeyboardShortcuts();

// 自动添加样式
KeyboardShortcuts.createStyles();
