/* 响应式设计样式 */
@media (max-width: 768px) {
    .container {
        padding: 20px 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }

    .filters {
        justify-content: center;
    }

    .results-header {
        flex-direction: column;
        gap: 15px;
    }

    .results-controls {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .sort-controls {
        justify-content: center;
    }

    .view-toggle {
        justify-content: center;
    }

    .search-input {
        padding: 14px 50px 14px 16px;
        font-size: 0.9rem;
    }

    .search-btn {
        width: 36px;
        height: 36px;
        right: 6px;
    }

    .filter-btn {
        padding: 6px 16px;
        font-size: 0.8rem;
    }

    .result-card {
        padding: 16px;
    }

    .file-icon {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }

    .file-title {
        font-size: 0.9rem;
    }

    .file-meta {
        font-size: 0.8rem;
    }

    .action-btn {
        padding: 5px 10px;
        font-size: 0.75rem;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 8px;
    }

    .page-btn {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 15px 10px;
    }

    .header h1 {
        font-size: 1.8rem;
    }

    .header p {
        font-size: 1rem;
    }

    .search-box {
        margin-bottom: 20px;
    }

    .filters {
        gap: 10px;
    }

    .filter-btn {
        padding: 5px 12px;
        font-size: 0.75rem;
    }

    .results-controls {
        gap: 10px;
    }

    .sort-select {
        padding: 5px 10px;
        font-size: 0.8rem;
    }

    .view-btn {
        padding: 6px 10px;
        font-size: 0.9rem;
    }

    .result-card {
        padding: 12px;
    }

    .card-header {
        margin-bottom: 10px;
    }

    .file-icon {
        width: 32px;
        height: 32px;
        margin-right: 10px;
        font-size: 0.9rem;
    }

    .file-title {
        font-size: 0.85rem;
        line-height: 1.3;
    }

    .file-meta {
        font-size: 0.75rem;
        margin-bottom: 10px;
    }

    .file-actions {
        gap: 8px;
        flex-wrap: wrap;
    }

    .action-btn {
        padding: 4px 8px;
        font-size: 0.7rem;
    }

    .platform-badge {
        padding: 1px 6px;
        font-size: 0.65rem;
        margin-left: 6px;
    }

    .pagination {
        gap: 6px;
    }

    .page-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .loading,
    .error,
    .empty {
        padding: 30px 15px;
    }

    .empty h3 {
        font-size: 1.3rem;
    }

    .empty p {
        font-size: 0.9rem;
    }
}
