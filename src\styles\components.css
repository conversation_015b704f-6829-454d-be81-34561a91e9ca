/* 筛选器组件样式 */
.filters {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.filter-btn .count {
    margin-left: 5px;
    opacity: 0.7;
    font-size: 0.8rem;
}

.filters.loading .filter-btn:not([data-type='all']) {
    opacity: 0.5;
    pointer-events: none;
}

/* 结果区域样式 */
.results-section {
    margin-top: 40px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.results-count {
    font-size: 0.9rem;
    opacity: 0.8;
}

.results-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-select {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
}

.sort-select:hover,
.sort-select:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.sort-select option {
    background: #333;
    color: white;
}

.view-toggle {
    display: flex;
    gap: 10px;
}

.view-btn {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active {
    background: rgba(255, 255, 255, 0.2);
}

/* 结果网格样式 */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.result-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 1.2rem;
}

.file-title {
    font-weight: 500;
    font-size: 1rem;
    line-height: 1.4;
    flex: 1;
}

.file-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    opacity: 0.8;
    margin-bottom: 12px;
}

.file-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 平台标签样式 */
.platform-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    margin-left: 8px;
}

.platform-tianyi {
    background: #ff6b35;
}
.platform-baidu {
    background: #2196f3;
}
.platform-aliyun {
    background: #ff9800;
}
.platform-default {
    background: rgba(255, 255, 255, 0.2);
}

/* 分页组件样式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 40px;
}

.page-btn {
    padding: 10px 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn:hover,
.page-btn.active {
    background: rgba(255, 255, 255, 0.2);
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
