/**
 * 生成完整的 HTML 页面
 * 将所有 CSS 和 JavaScript 内联到 HTML 中
 * @returns {string} 完整的 HTML 字符串
 */
export async function generateHTML() {
    try {
        const html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网盘搜索 - File Storage</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 16px;
            opacity: 0.95;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.8;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .search-section {
            margin-bottom: 40px;
        }

        .search-box {
            position: relative;
            max-width: 800px;
            margin: 0 auto 30px;
        }

        .search-input {
            width: 100%;
            padding: 16px 60px 16px 20px;
            font-size: 1rem;
            border: none;
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            color: white;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) scale(1.1);
        }

        .filters {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .results-section {
            margin-top: 40px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .results-count {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .results-controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .sort-select {
            padding: 6px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
        }

        .sort-select option {
            background: #333;
            color: white;
        }

        .view-btn {
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .view-btn.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .result-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .result-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .file-title {
            font-weight: 500;
            font-size: 1rem;
            line-height: 1.4;
            flex: 1;
        }

        .file-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            opacity: 0.8;
            margin-bottom: 12px;
        }

        .file-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.1rem;
        }

        .error {
            text-align: center;
            padding: 40px;
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 12px;
            margin: 20px 0;
        }

        .empty {
            text-align: center;
            padding: 60px 20px;
            opacity: 0.7;
        }

        .empty h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            .header h1 {
                font-size: 2rem;
            }
            .results-grid {
                grid-template-columns: 1fr;
            }
            .results-header {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>File Storage</h1>
            <p>发现、整理和管理您的文件，享受直观而强大的搜索体验</p>
        </div>
        
        <div class="search-section">
            <div class="search-box">
                <input type="text" class="search-input" placeholder="搜索文件、文件夹和内容..." id="searchInput">
                <button class="search-btn" id="searchBtn">🔍</button>
            </div>
            
            <div class="filters" id="dynamicFilters">
                <button class="filter-btn active" data-type="all">所有文件</button>
            </div>
        </div>
        
        <div class="results-section" id="resultsSection" style="display: none;">
            <div class="results-header">
                <div class="results-count" id="resultsCount">找到 0 个文件</div>
                <div class="results-controls">
                    <div class="sort-controls">
                        <select class="sort-select" id="sortSelect">
                            <option value="default">默认排序</option>
                            <option value="newest">最新优先</option>
                            <option value="oldest">最旧优先</option>
                        </select>
                    </div>
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid">⊞</button>
                        <button class="view-btn" data-view="list">☰</button>
                    </div>
                </div>
            </div>
            
            <div class="results-grid" id="resultsGrid"></div>
            <div class="pagination" id="pagination"></div>
        </div>
        
        <div class="loading" id="loading" style="display: none;">
            <div>🔍 正在搜索...</div>
        </div>
        
        <div class="error" id="error" style="display: none;"></div>
        
        <div class="empty" id="empty">
            <h3>开始搜索</h3>
            <p>输入关键词来搜索网盘资源</p>
        </div>
    </div>

    <script>
        // 简化版本的 JavaScript 代码
        (function() {
            'use strict';
            
            class PanSearchApp {
                constructor() {
                    this.currentResults = [];
                    this.init();
                }

                init() {
                    this.bindEvents();
                }

                bindEvents() {
                    const searchBtn = document.getElementById('searchBtn');
                    if (searchBtn) {
                        searchBtn.addEventListener('click', () => this.search());
                    }

                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') this.search();
                        });
                    }
                }

                async search() {
                    const keyword = document.getElementById('searchInput').value.trim();
                    if (!keyword) return;

                    this.showLoading();

                    try {
                        const response = await fetch('/api/search?kw=' + encodeURIComponent(keyword));
                        const data = await response.json();

                        if (data.code === 0) {
                            this.currentResults = this.processResults(data.data);
                            this.renderResults();
                        } else {
                            this.showError('搜索失败：' + (data.message || '未知错误'));
                        }
                    } catch (error) {
                        this.showError('网络错误，请稍后重试');
                    }
                }

                processResults(data) {
                    const results = [];
                    if (data.merged_by_type) {
                        Object.entries(data.merged_by_type).forEach(([platform, items]) => {
                            items.forEach(item => {
                                results.push({
                                    ...item,
                                    platform: platform,
                                    platformName: this.getPlatformName(platform)
                                });
                            });
                        });
                    }
                    return results;
                }

                getPlatformName(platform) {
                    const names = {
                        'tianyi': '天翼网盘',
                        'baidu': '百度网盘',
                        'aliyun': '阿里云盘'
                    };
                    return names[platform] || platform;
                }

                renderResults() {
                    this.hideAll();
                    document.getElementById('resultsSection').style.display = 'block';
                    document.getElementById('resultsCount').textContent = '找到 ' + this.currentResults.length + ' 个文件';
                    
                    const grid = document.getElementById('resultsGrid');
                    grid.innerHTML = this.currentResults.slice(0, 12).map(item => this.createResultCard(item)).join('');
                }

                createResultCard(item) {
                    const date = item.datetime ? new Date(item.datetime).toLocaleDateString() : '未知';
                    return '<div class="result-card">' +
                        '<div class="card-header">' +
                            '<div class="file-title">' + (item.note || '未命名文件') + '</div>' +
                        '</div>' +
                        '<div class="file-meta">' +
                            '<span>' + date + '</span>' +
                            '<span>' + item.platformName + '</span>' +
                        '</div>' +
                        '<div class="file-actions">' +
                            '<a href="' + item.url + '" target="_blank" class="action-btn">打开链接</a>' +
                        '</div>' +
                    '</div>';
                }

                showLoading() {
                    this.hideAll();
                    document.getElementById('loading').style.display = 'block';
                }

                showError(message) {
                    this.hideAll();
                    const errorEl = document.getElementById('error');
                    errorEl.textContent = message;
                    errorEl.style.display = 'block';
                }

                hideAll() {
                    document.getElementById('resultsSection').style.display = 'none';
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('error').style.display = 'none';
                    document.getElementById('empty').style.display = 'none';
                }
            }

            // 初始化应用
            window.app = new PanSearchApp();
        })();
    </script>
</body>
</html>`;

        return html;
    } catch (error) {
        console.error('HTML generation error:', error);
        throw error;
    }
}
