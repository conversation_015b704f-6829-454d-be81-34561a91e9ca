import js from '@eslint/js';

export default [
    js.configs.recommended,
    {
        languageOptions: {
            ecmaVersion: 2022,
            sourceType: 'module',
            globals: {
                // Browser globals
                window: 'readonly',
                document: 'readonly',
                console: 'readonly',
                fetch: 'readonly',
                URL: 'readonly',
                URLSearchParams: 'readonly',
                Response: 'readonly',
                Request: 'readonly',
                // Cloudflare Workers globals
                addEventListener: 'readonly',
                caches: 'readonly',
                crypto: 'readonly',
                btoa: 'readonly',
                atob: 'readonly',
                // Node.js/Browser globals
                setTimeout: 'readonly',
                clearTimeout: 'readonly',
                setInterval: 'readonly',
                clearInterval: 'readonly',
                navigator: 'readonly'
            }
        },
        rules: {
            // 代码质量规则
            'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
            'no-console': 'warn',
            'no-debugger': 'error',
            'no-alert': 'warn',

            // 代码风格规则
            indent: ['error', 4],
            quotes: ['error', 'single'],
            semi: ['error', 'always'],
            'comma-dangle': ['error', 'never'],
            'no-trailing-spaces': 'error',
            'eol-last': 'error',

            // ES6+ 规则
            'prefer-const': 'error',
            'no-var': 'error',
            'arrow-spacing': 'error',
            'template-curly-spacing': 'error',

            // 最佳实践
            eqeqeq: 'error',
            curly: 'error',
            'no-eval': 'error',
            'no-implied-eval': 'error'
        }
    },
    {
        files: ['**/*.test.js', '**/*.spec.js'],
        languageOptions: {
            globals: {
                describe: 'readonly',
                it: 'readonly',
                test: 'readonly',
                expect: 'readonly',
                beforeEach: 'readonly',
                afterEach: 'readonly',
                beforeAll: 'readonly',
                afterAll: 'readonly',
                jest: 'readonly'
            }
        }
    },
    {
        files: ['src/build/**/*.js', 'webpack.config.js'],
        languageOptions: {
            globals: {
                require: 'readonly',
                module: 'readonly',
                exports: 'readonly',
                __dirname: 'readonly',
                __filename: 'readonly',
                process: 'readonly',
                Buffer: 'readonly',
                global: 'readonly'
            }
        }
    },
    {
        files: ['src/api/**/*.js', 'src/worker.js'],
        languageOptions: {
            globals: {
                AbortSignal: 'readonly',
                fetch: 'readonly',
                Request: 'readonly',
                Response: 'readonly',
                URL: 'readonly',
                URLSearchParams: 'readonly'
            }
        }
    }
];
