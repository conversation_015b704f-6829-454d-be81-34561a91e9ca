/**
 * 虚拟滚动组件
 * 用于高效渲染大量列表项，只渲染可见区域的项目
 */
export class VirtualScroll {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            itemHeight: 120, // 每个项目的高度
            bufferSize: 5, // 缓冲区大小（上下各多渲染几个项目）
            threshold: 0.1, // Intersection Observer 阈值
            ...options
        };
        
        this.items = [];
        this.visibleStart = 0;
        this.visibleEnd = 0;
        this.scrollTop = 0;
        this.containerHeight = 0;
        
        this.init();
    }

    init() {
        this.setupContainer();
        this.setupIntersectionObserver();
        this.bindEvents();
    }

    setupContainer() {
        // 设置容器样式
        this.container.style.position = 'relative';
        this.container.style.overflow = 'auto';
        
        // 创建内容容器
        this.contentContainer = document.createElement('div');
        this.contentContainer.className = 'virtual-scroll-content';
        this.contentContainer.style.position = 'relative';
        
        // 创建占位容器（用于维持滚动条高度）
        this.spacerContainer = document.createElement('div');
        this.spacerContainer.className = 'virtual-scroll-spacer';
        this.spacerContainer.style.position = 'absolute';
        this.spacerContainer.style.top = '0';
        this.spacerContainer.style.left = '0';
        this.spacerContainer.style.right = '0';
        this.spacerContainer.style.pointerEvents = 'none';
        
        this.container.appendChild(this.spacerContainer);
        this.container.appendChild(this.contentContainer);
        
        this.updateContainerHeight();
    }

    setupIntersectionObserver() {
        // 创建 Intersection Observer 来监听滚动
        this.observer = new IntersectionObserver(
            (entries) => this.handleIntersection(entries),
            {
                root: this.container,
                threshold: this.options.threshold
            }
        );
    }

    bindEvents() {
        // 监听容器滚动事件
        this.container.addEventListener('scroll', () => {
            this.handleScroll();
        });

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.updateContainerHeight();
            this.render();
        });
    }

    handleScroll() {
        this.scrollTop = this.container.scrollTop;
        this.calculateVisibleRange();
        this.render();
    }

    handleIntersection(entries) {
        // 处理 Intersection Observer 回调
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // 项目进入可视区域
                const index = parseInt(entry.target.dataset.index);
                this.onItemVisible(index);
            }
        });
    }

    calculateVisibleRange() {
        this.updateContainerHeight();
        
        const startIndex = Math.floor(this.scrollTop / this.options.itemHeight);
        const endIndex = Math.min(
            startIndex + Math.ceil(this.containerHeight / this.options.itemHeight),
            this.items.length - 1
        );

        // 添加缓冲区
        this.visibleStart = Math.max(0, startIndex - this.options.bufferSize);
        this.visibleEnd = Math.min(this.items.length - 1, endIndex + this.options.bufferSize);
    }

    updateContainerHeight() {
        this.containerHeight = this.container.clientHeight;
    }

    setItems(items) {
        this.items = items;
        this.updateSpacerHeight();
        this.calculateVisibleRange();
        this.render();
    }

    updateSpacerHeight() {
        const totalHeight = this.items.length * this.options.itemHeight;
        this.spacerContainer.style.height = `${totalHeight}px`;
    }

    render() {
        if (!this.items.length) {
            this.contentContainer.innerHTML = '';
            return;
        }

        const fragment = document.createDocumentFragment();
        
        for (let i = this.visibleStart; i <= this.visibleEnd; i++) {
            const item = this.items[i];
            if (!item) continue;

            const element = this.renderItem(item, i);
            element.style.position = 'absolute';
            element.style.top = `${i * this.options.itemHeight}px`;
            element.style.left = '0';
            element.style.right = '0';
            element.style.height = `${this.options.itemHeight}px`;
            element.dataset.index = i;

            // 添加到 Intersection Observer
            this.observer.observe(element);
            
            fragment.appendChild(element);
        }

        // 清空并重新填充内容
        this.contentContainer.innerHTML = '';
        this.contentContainer.appendChild(fragment);
    }

    renderItem(item, index) {
        // 默认渲染方法，子类应该重写此方法
        const element = document.createElement('div');
        element.className = 'virtual-scroll-item';
        element.innerHTML = `<div>Item ${index}: ${JSON.stringify(item)}</div>`;
        return element;
    }

    onItemVisible(index) {
        // 项目变为可见时的回调，子类可以重写
        console.log(`Item ${index} is now visible`);
    }

    scrollToIndex(index) {
        if (index < 0 || index >= this.items.length) return;
        
        const targetScrollTop = index * this.options.itemHeight;
        this.container.scrollTop = targetScrollTop;
    }

    scrollToTop() {
        this.container.scrollTop = 0;
    }

    getVisibleRange() {
        return {
            start: this.visibleStart,
            end: this.visibleEnd
        };
    }

    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
        
        // 移除事件监听器
        this.container.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.updateContainerHeight);
        
        // 清空容器
        this.container.innerHTML = '';
    }
}
