import path from 'path';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default (env, argv) => {
    const isProduction = argv.mode === 'production';

    return {
        entry: './src/worker.js',
        output: {
            filename: 'worker.js',
            path: path.resolve(__dirname, '../../dist'),
            clean: true,
            library: {
                type: 'module'
            }
        },
        experiments: {
            outputModule: true
        },
        mode: isProduction ? 'production' : 'development',
        devtool: isProduction ? false : 'source-map',
        module: {
            rules: [
                {
                    test: /\.js$/,
                    exclude: /node_modules/,
                    use: {
                        loader: 'babel-loader',
                        options: {
                            presets: [
                                ['@babel/preset-env', {
                                    targets: {
                                        browsers: ['> 1%', 'last 2 versions']
                                    },
                                    modules: false
                                }]
                            ]
                        }
                    }
                },
                {
                    test: /\.css$/,
                    use: ['style-loader', 'css-loader']
                },
                {
                    test: /\.html$/,
                    use: 'html-loader'
                }
            ]
        },
        plugins: [
            new HtmlWebpackPlugin({
                template: './src/templates/index.html',
                filename: 'index.html',
                inject: false,
                minify: isProduction ? {
                    removeComments: true,
                    collapseWhitespace: true,
                    removeRedundantAttributes: true,
                    useShortDoctype: true,
                    removeEmptyAttributes: true,
                    removeStyleLinkTypeAttributes: true,
                    keepClosingSlash: true,
                    minifyJS: true,
                    minifyCSS: true,
                    minifyURLs: true
                } : false
            })
        ],
        resolve: {
            extensions: ['.js', '.json'],
            alias: {
                '@': path.resolve(__dirname, '../'),
                '@components': path.resolve(__dirname, '../scripts/components'),
                '@utils': path.resolve(__dirname, '../scripts/utils'),
                '@config': path.resolve(__dirname, '../config'),
                '@styles': path.resolve(__dirname, '../styles')
            }
        },
        optimization: {
            minimize: isProduction,
            usedExports: true,
            sideEffects: false,
            splitChunks: {
                chunks: 'all',
                cacheGroups: {
                    // 第三方库
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                        priority: 10
                    },
                    // 工具函数
                    utils: {
                        test: /[\\/]src[\\/]scripts[\\/]utils[\\/]/,
                        name: 'utils',
                        chunks: 'all',
                        priority: 5
                    },
                    // 组件
                    components: {
                        test: /[\\/]src[\\/]scripts[\\/]components[\\/]/,
                        name: 'components',
                        chunks: 'all',
                        priority: 5
                    },
                    // 默认
                    default: {
                        minChunks: 2,
                        priority: -10,
                        reuseExistingChunk: true
                    }
                }
            }
        },
        target: 'webworker',
        stats: {
            colors: true,
            modules: false,
            children: false,
            chunks: false,
            chunkModules: false
        }
    };
};
