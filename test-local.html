<!doctype html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>网盘搜索 - 本地测试版</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: white;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 40px 20px;
            }

            .header {
                text-align: center;
                margin-bottom: 40px;
            }

            .header h1 {
                font-size: 3rem;
                font-weight: 300;
                margin-bottom: 16px;
                opacity: 0.95;
            }

            .header p {
                font-size: 1.1rem;
                opacity: 0.8;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            }

            .search-section {
                margin-bottom: 40px;
            }

            .search-box {
                position: relative;
                max-width: 800px;
                margin: 0 auto 30px;
            }

            .search-input {
                width: 100%;
                padding: 16px 60px 16px 20px;
                font-size: 1rem;
                border: none;
                border-radius: 50px;
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(10px);
                color: white;
                outline: none;
                transition: all 0.3s ease;
            }

            .search-input::placeholder {
                color: rgba(255, 255, 255, 0.7);
            }

            .search-input:focus {
                background: rgba(255, 255, 255, 0.25);
                transform: translateY(-2px);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            }

            .search-btn {
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
                background: rgba(255, 255, 255, 0.2);
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .search-btn:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-50%) scale(1.1);
            }

            .filters {
                display: flex;
                justify-content: center;
                gap: 15px;
                flex-wrap: wrap;
            }

            .filter-btn {
                padding: 8px 20px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 25px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 0.9rem;
            }

            .filter-btn:hover,
            .filter-btn.active {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.5);
            }

            .filter-btn .count {
                margin-left: 5px;
                opacity: 0.7;
                font-size: 0.8rem;
            }

            .results-controls {
                display: flex;
                align-items: center;
                gap: 20px;
            }

            .sort-controls {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .sort-select {
                padding: 6px 12px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                font-size: 0.9rem;
                cursor: pointer;
                transition: all 0.3s ease;
                outline: none;
            }

            .sort-select:hover,
            .sort-select:focus {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.5);
            }

            .sort-select option {
                background: #333;
                color: white;
            }

            .platform-badge {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.7rem;
                font-weight: 500;
                margin-left: 8px;
            }

            .platform-tianyi {
                background: #ff6b35;
            }

            .platform-baidu {
                background: #2196f3;
            }

            .platform-aliyun {
                background: #ff9800;
            }

            .platform-quark {
                background: #9c27b0;
            }

            .platform-default {
                background: rgba(255, 255, 255, 0.2);
            }

            .demo-notice {
                background: rgba(255, 193, 7, 0.2);
                border: 1px solid rgba(255, 193, 7, 0.5);
                border-radius: 12px;
                padding: 20px;
                margin: 20px 0;
                text-align: center;
            }

            .demo-notice h3 {
                margin-bottom: 10px;
                color: #ffc107;
            }

            .empty {
                text-align: center;
                padding: 60px 20px;
                opacity: 0.7;
            }

            .empty h3 {
                font-size: 1.5rem;
                margin-bottom: 10px;
            }

            @media (max-width: 768px) {
                .container {
                    padding: 20px 15px;
                }

                .header h1 {
                    font-size: 2rem;
                }

                .filters {
                    justify-content: center;
                }
            }
        </style>
    </head>

    <body>
        <div class="container">
            <div class="header">
                <h1>File Storage</h1>
                <p>发现、整理和管理您的文件，享受直观而强大的搜索体验</p>
            </div>

            <div class="demo-notice">
                <h3>⚠️ 本地测试版本</h3>
                <p>这是界面预览版本。要使用完整功能，请部署到 Cloudflare Workers。</p>
                <p>部署命令：<code>npm run deploy</code></p>
            </div>

            <div class="search-section">
                <div class="search-box">
                    <input
                        type="text"
                        class="search-input"
                        placeholder="搜索文件、文件夹和内容..."
                        id="searchInput"
                    />
                    <button class="search-btn" id="searchBtn">🔍</button>
                </div>

                <div class="filters" id="dynamicFilters">
                    <button class="filter-btn active" data-type="all">
                        所有文件 <span class="count">(0)</span>
                    </button>
                    <button class="filter-btn" data-type="tianyi">
                        天翼网盘 <span class="count">(15)</span>
                    </button>
                    <button class="filter-btn" data-type="baidu">
                        百度网盘 <span class="count">(8)</span>
                    </button>
                    <button class="filter-btn" data-type="aliyun">
                        阿里云盘 <span class="count">(12)</span>
                    </button>
                    <button class="filter-btn" data-type="quark">
                        夸克网盘 <span class="count">(5)</span>
                    </button>
                </div>
            </div>

            <div class="results-section" style="display: block">
                <div class="results-header">
                    <div class="results-count">找到 40 个文件</div>
                    <div class="results-controls">
                        <div class="sort-controls">
                            <select class="sort-select" id="sortSelect">
                                <option value="default">默认排序</option>
                                <option value="newest">最新优先</option>
                                <option value="oldest">最旧优先</option>
                            </select>
                        </div>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="grid">⊞</button>
                            <button class="view-btn" data-view="list">☰</button>
                        </div>
                    </div>
                </div>

                <div class="results-grid">
                    <!-- 示例结果卡片 -->
                    <div class="result-card">
                        <div class="card-header">
                            <div class="file-icon" style="background: #dc3545">📄</div>
                            <div class="file-title">重要文档.pdf</div>
                        </div>
                        <div class="file-meta">
                            <span>2024-01-15</span>
                            <span class="platform-badge platform-tianyi">天翼网盘</span>
                        </div>
                        <div class="file-actions">
                            <a href="#" class="action-btn">打开链接</a>
                            <span class="action-btn">密码: abc123</span>
                        </div>
                    </div>

                    <div class="result-card">
                        <div class="card-header">
                            <div class="file-icon" style="background: #0d6efd">📝</div>
                            <div class="file-title">项目资料.docx</div>
                        </div>
                        <div class="file-meta">
                            <span>2024-01-10</span>
                            <span class="platform-badge platform-baidu">百度网盘</span>
                        </div>
                        <div class="file-actions">
                            <a href="#" class="action-btn">打开链接</a>
                        </div>
                    </div>

                    <div class="result-card">
                        <div class="card-header">
                            <div class="file-icon" style="background: #e83e8c">🎬</div>
                            <div class="file-title">教学视频.mp4</div>
                        </div>
                        <div class="file-meta">
                            <span>2024-01-08</span>
                            <span class="platform-badge platform-aliyun">阿里云盘</span>
                        </div>
                        <div class="file-actions">
                            <a href="#" class="action-btn">打开链接</a>
                            <span class="action-btn">密码: xyz789</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="empty" id="empty">
                <h3>界面预览</h3>
                <p>这是网盘搜索应用的界面预览。部署到 Cloudflare Workers 后即可使用完整功能。</p>
            </div>
        </div>

        <script>
            // 本地测试版本 - 仅用于界面预览
            document.getElementById('searchBtn').addEventListener('click', () => {
                alert('这是本地预览版本。要使用搜索功能，请部署到 Cloudflare Workers。');
            });

            document.getElementById('searchInput').addEventListener('keypress', e => {
                if (e.key === 'Enter') {
                    alert('这是本地预览版本。要使用搜索功能，请部署到 Cloudflare Workers。');
                }
            });

            // 筛选按钮交互
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', e => {
                    document
                        .querySelectorAll('.filter-btn')
                        .forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                });
            });
        </script>
    </body>
</html>
