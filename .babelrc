{"presets": [["@babel/preset-env", {"targets": {"browsers": ["> 1%", "last 2 versions"]}, "modules": false, "useBuiltIns": "usage", "corejs": 3}]], "plugins": [], "env": {"development": {"presets": [["@babel/preset-env", {"targets": {"browsers": ["> 1%", "last 2 versions"]}, "modules": false, "debug": true}]]}, "production": {"presets": [["@babel/preset-env", {"targets": {"browsers": ["> 1%", "last 2 versions"]}, "modules": false}]]}}}