/**
 * 动态导入工具
 * 提供按需加载功能模块的能力
 */
export class DynamicImporter {
    constructor() {
        this.cache = new Map();
        this.loading = new Map();
    }

    /**
     * 动态导入模块
     * @param {string} modulePath - 模块路径
     * @returns {Promise<any>} 模块对象
     */
    async import(modulePath) {
        // 检查缓存
        if (this.cache.has(modulePath)) {
            return this.cache.get(modulePath);
        }

        // 检查是否正在加载
        if (this.loading.has(modulePath)) {
            return this.loading.get(modulePath);
        }

        // 开始加载
        const loadingPromise = this.loadModule(modulePath);
        this.loading.set(modulePath, loadingPromise);

        try {
            const module = await loadingPromise;
            this.cache.set(modulePath, module);
            this.loading.delete(modulePath);
            return module;
        } catch (error) {
            this.loading.delete(modulePath);
            throw error;
        }
    }

    /**
     * 加载模块
     * @param {string} modulePath - 模块路径
     * @returns {Promise<any>} 模块对象
     */
    async loadModule(modulePath) {
        try {
            const module = await import(modulePath);
            return module;
        } catch (error) {
            console.error(`Failed to load module: ${modulePath}`, error);
            throw new Error(`Module loading failed: ${modulePath}`);
        }
    }

    /**
     * 预加载模块
     * @param {string[]} modulePaths - 模块路径数组
     * @returns {Promise<void>}
     */
    async preload(modulePaths) {
        const promises = modulePaths.map(path => this.import(path));
        await Promise.allSettled(promises);
    }

    /**
     * 清除缓存
     * @param {string} modulePath - 模块路径，不传则清除所有
     */
    clearCache(modulePath = null) {
        if (modulePath) {
            this.cache.delete(modulePath);
        } else {
            this.cache.clear();
        }
    }

    /**
     * 获取缓存状态
     * @returns {Object} 缓存信息
     */
    getCacheInfo() {
        return {
            cached: Array.from(this.cache.keys()),
            loading: Array.from(this.loading.keys()),
            cacheSize: this.cache.size
        };
    }
}

// 创建全局动态导入实例
export const dynamicImporter = new DynamicImporter();

/**
 * 功能模块懒加载管理器
 */
export class FeatureLoader {
    constructor() {
        this.features = new Map();
        this.setupFeatures();
    }

    setupFeatures() {
        // 定义可懒加载的功能模块
        this.features.set('virtualScroll', {
            path: '../components/VirtualScroll.js',
            description: '虚拟滚动功能'
        });

        this.features.set('virtualResultGrid', {
            path: '../components/VirtualResultGrid.js',
            description: '虚拟搜索结果网格'
        });

        this.features.set('lazyLoader', {
            path: '../utils/lazy-loader.js',
            description: '懒加载工具'
        });

        this.features.set('skeletonLoader', {
            path: '../components/SkeletonLoader.js',
            description: '骨架屏加载'
        });

        this.features.set('searchSuggestions', {
            path: '../components/SearchSuggestions.js',
            description: '搜索建议'
        });

        this.features.set('keyboardShortcuts', {
            path: '../utils/keyboard-shortcuts.js',
            description: '键盘快捷键'
        });
    }

    /**
     * 加载功能模块
     * @param {string} featureName - 功能名称
     * @returns {Promise<any>} 功能模块
     */
    async loadFeature(featureName) {
        const feature = this.features.get(featureName);
        if (!feature) {
            throw new Error(`Unknown feature: ${featureName}`);
        }

        console.log(`Loading feature: ${feature.description}`);
        return await dynamicImporter.import(feature.path);
    }

    /**
     * 批量加载功能模块
     * @param {string[]} featureNames - 功能名称数组
     * @returns {Promise<Object>} 功能模块对象
     */
    async loadFeatures(featureNames) {
        const results = {};
        const promises = featureNames.map(async (name) => {
            try {
                results[name] = await this.loadFeature(name);
            } catch (error) {
                console.error(`Failed to load feature ${name}:`, error);
                results[name] = null;
            }
        });

        await Promise.allSettled(promises);
        return results;
    }

    /**
     * 预加载核心功能
     * @returns {Promise<void>}
     */
    async preloadCore() {
        const coreFeatures = ['virtualScroll', 'lazyLoader', 'skeletonLoader'];
        await this.loadFeatures(coreFeatures);
    }

    /**
     * 预加载增强功能
     * @returns {Promise<void>}
     */
    async preloadEnhanced() {
        const enhancedFeatures = ['searchSuggestions', 'keyboardShortcuts'];
        await this.loadFeatures(enhancedFeatures);
    }

    /**
     * 获取可用功能列表
     * @returns {Array} 功能列表
     */
    getAvailableFeatures() {
        return Array.from(this.features.entries()).map(([name, feature]) => ({
            name,
            description: feature.description
        }));
    }
}

// 创建全局功能加载器实例
export const featureLoader = new FeatureLoader();

/**
 * 便捷的懒加载函数
 */

/**
 * 懒加载虚拟滚动
 * @returns {Promise<any>} VirtualScroll 类
 */
export async function loadVirtualScroll() {
    const module = await featureLoader.loadFeature('virtualScroll');
    return module.VirtualScroll;
}

/**
 * 懒加载虚拟结果网格
 * @returns {Promise<any>} VirtualResultGrid 类
 */
export async function loadVirtualResultGrid() {
    const module = await featureLoader.loadFeature('virtualResultGrid');
    return module.VirtualResultGrid;
}

/**
 * 懒加载懒加载工具
 * @returns {Promise<any>} LazyLoader 类
 */
export async function loadLazyLoader() {
    const module = await featureLoader.loadFeature('lazyLoader');
    return module.LazyLoader;
}

/**
 * 懒加载骨架屏
 * @returns {Promise<any>} SkeletonLoader 类
 */
export async function loadSkeletonLoader() {
    const module = await featureLoader.loadFeature('skeletonLoader');
    return module.SkeletonLoader;
}

/**
 * 懒加载搜索建议
 * @returns {Promise<any>} SearchSuggestions 类
 */
export async function loadSearchSuggestions() {
    const module = await featureLoader.loadFeature('searchSuggestions');
    return module.SearchSuggestions;
}

/**
 * 懒加载键盘快捷键
 * @returns {Promise<any>} KeyboardShortcuts 类
 */
export async function loadKeyboardShortcuts() {
    const module = await featureLoader.loadFeature('keyboardShortcuts');
    return module.KeyboardShortcuts;
}

/**
 * 根据用户交互懒加载功能
 */
export class InteractionBasedLoader {
    constructor() {
        this.loadedFeatures = new Set();
        this.setupTriggers();
    }

    setupTriggers() {
        // 当用户开始搜索时加载核心功能
        document.addEventListener('DOMContentLoaded', () => {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('focus', () => {
                    this.loadOnFirstInteraction();
                }, { once: true });
            }
        });

        // 当用户滚动时加载虚拟滚动
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            if (!this.loadedFeatures.has('virtualScroll')) {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    this.loadVirtualScrollFeatures();
                }, 100);
            }
        });
    }

    async loadOnFirstInteraction() {
        if (this.loadedFeatures.has('core')) return;
        
        console.log('Loading core features on first interaction');
        await featureLoader.preloadCore();
        this.loadedFeatures.add('core');
    }

    async loadVirtualScrollFeatures() {
        if (this.loadedFeatures.has('virtualScroll')) return;
        
        console.log('Loading virtual scroll features');
        await featureLoader.loadFeatures(['virtualScroll', 'virtualResultGrid']);
        this.loadedFeatures.add('virtualScroll');
    }

    async loadEnhancedFeatures() {
        if (this.loadedFeatures.has('enhanced')) return;
        
        console.log('Loading enhanced features');
        await featureLoader.preloadEnhanced();
        this.loadedFeatures.add('enhanced');
    }
}

// 创建全局交互式加载器实例
export const interactionLoader = new InteractionBasedLoader();
